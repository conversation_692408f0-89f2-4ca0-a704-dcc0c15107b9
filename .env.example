# ==========
# 通用设置
# ==========
# 是否开启生产模式（默认开启）
PROD_MODE=
# 企业微信通知Webhook Key
WEWORK_WEBHOOK_KEY=

# API Docs
API_DOCS_AUTH_USERNAME=
API_DOCS_AUTH_PASSWORD=

# OpenAI服务
OPENAI_API_KEY=
OPENAI_BASE_URL=

# deepseek
DEEPSEEK_BASE_URL=
DEEPSEEK_API_KEY=

# 智普
ZHIPU_BASE_URL=
ZHIPU_API_KEY=

# 豆包
DOUBAO_BASE_URL=
DOUBAO_API_KEY=

# 千问
QWEN_BASE_URL=
QWEN_API_KEY=

# Azure AI 配置
AZURE_AI_ENDPOINT=
AZURE_AI_API_KEY=
AZURE_AI_API_VERSION=

# 聚合API服务
JUHE_WEATHER_SERVICE_KEY=
JUHE_IP_SERVICE_KEY=

# Langfuse大模型调用日志追踪
LANGFUSE_SECRET_KEY=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_HOST=

# Elasticsearch 日志
ES_BASE_URL=
# Elasticsearch 文档索引前缀，默认为 xiaoxing-ai
ES_INDEX_PREFIX=

# 数据库
DATABASE_HOST=
DATABASE_PORT=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_DBNAME=

# 广州侧 Database
GZ_DATABASE_HOST=
GZ_DATABASE_PORT=
GZ_DATABASE_USERNAME=
GZ_DATABASE_PASSWORD=
GZ_DATABASE_DBNAME=

# AI英语/作文的数据库
YYPX_DATABASE_HOST=
YYPX_DATABASE_PORT=
YYPX_DATABASE_USERNAME=
YYPX_DATABASE_PASSWORD=
YYPX_DATABASE_DBNAME=

# 百度ocr
BAIDU_OCR_API_KEY=
BAIDU_OCR_SECRET_KEY=

# oss
OSS_CDN_URL=
OSS_ENTRYPOINT=
OSS_ACCESS_KEY=
OSS_SECRET_KEY=
OSS_BUCKET_NAME=
OSS_BUCKET_REGION=

# 讯飞
XF_APP_ID=
XF_API_KEY=
XF_API_SECRET=

# 阿里云OCR配置
ALIBABA_CLOUD_ACCESS_KEY_ID=
ALIBABA_CLOUD_ACCESS_KEY_SECRET=
ALIBABA_CLOUD_OCR_ENDPOINT=

# 学科网API配置
XKW_API_BASE=
XKW_APP_KEY=
XKW_APP_SECRET=

# Redis
REDIS_HOST=
REDIS_PORT=
REDIS_DB=
REDIS_PASSWORD=