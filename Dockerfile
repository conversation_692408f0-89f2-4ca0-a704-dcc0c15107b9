ARG PYTHON_VERSION=3.12
FROM harbor.xiaoxingcloud.com/foundation/python:${PYTHON_VERSION}-slim AS base

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV TZ=Asia/Shanghai

RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources \
    && apt-get update -y \
    && apt-get install -y curl \
    && apt clean \
    && rm -rf /var/lib/apt/lists/*

RUN pip config set global.index-url https://mirrors.ustc.edu.cn/pypi/web/simple  \
    && pip install --upgrade pip \
    && pip install uv

ENV PATH="/root/.local/bin/:$PATH"
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy

FROM base AS builder

WORKDIR /app
COPY . /app

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev --no-cache
ENV PATH="/app/.venv/bin:$PATH"

EXPOSE 8000

CMD ["uv", "run", "--", "fastapi", "run", "xiaoxing_ai/main.py", "--port", "8000" ]
