# Makefile for the xiaoxing_ai project

# === Variables ===
# Using variables for highly repeated values makes changes easier.
RUN      := uv run
FILES    ?= ./xiaoxing_ai
RUFF     := $(RUN) ruff

# --- Default Target ---
# The default target that runs when you just type `make`.
.DEFAULT_GOAL := help

# === Main Targets ===

.PHONY: help
help: ## Show this help message
	@echo "Usage: make [target]"
	@echo ""
	@echo "Available targets:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | \
		awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-18s\033[0m %s\n", $$1, $$2}'

.PHONY: install
install: ## Install dependencies and pre-commit hooks
	@uv sync
	@$(RUN) pre-commit install

.PHONY: dev
dev: ## Start the development server in auto-reload mode
	@$(RUN) fastapi dev xiaoxing_ai

# --- Quality & Formatting ---

.PHONY: ruff-check
ruff-check: ## Check for linting errors with ruff
	@$(RUFF) check $(FILES)

.PHONY: ruff-check-fix
ruff-check-fix: ## Automatically fix linting errors with ruff
	@$(RUFF) check --fix $(FILES)

.PHONY: ruff-format
ruff-format: ## Format code with ruff's formatter
	@$(RUFF) format $(FILES)

.PHONY: ruff-all
ruff-all: ruff-check-fix ruff-format ## Run all ruff formatters and fixers

.PHONY: lint
lint: ## Run static type checking with pyright
	@$(RUN) pyright

.PHONY: lint-all
lint-all: ruff-check-fix lint ## Run all linters (ruff-fix and pyright)

# --- Testing ---

.PHONY: test
test: ## Run tests with pytest
	@$(RUN) pytest -n auto -vv -s $(ARGS)

# --- CI/All-in-one Target ---

.PHONY: all
all: ruff-format lint test ## Run formatter, type checker, and tests (ideal for CI)

# --- Housekeeping ---

.PHONY: clean
clean: ## Clean up Python cache files
	@find . -type f -name "*.py[co]" -delete
	@find . -type d -name "__pycache__" -delete
	@rm -rf .pytest_cache .ruff_cache .pyright_cache