# xiaoxing-ai

**开发环境搭建**

1. 安装 [uv](https://docs.astral.sh/uv/getting-started/installation/)
2. 安装 Make 命令
3. 运行以下命令安装依赖和设置 pre-commit：

    ```sh
    make install
    ```

    （可选）直接运行命令
    ```sh
    uv sync
    uv run pre-commit install
    ```

4. 配置环境变量，将 `.env.example` 复制为 `.env`，并修改其中的配置项：

    ```sh
    cp .env.example .env
    ```

5. 在开发环境中运行：

    ```sh
    make dev
    ```

    （可选）直接运行命令

    ```sh
    uv run fastapi dev xiaoxing_ai
    ```