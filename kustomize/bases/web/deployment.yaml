apiVersion: apps/v1
kind: Deployment
metadata:
  name: xiaoxing-ai
spec:
  minReadySeconds: 30
  template:
    spec:
      imagePullSecrets:
        - name: registry-pull-secret
      containers:
        - name: xiaoxing-ai
          image: xiaoxing-ai
          imagePullPolicy: IfNotPresent
          command: [ "/bin/sh", "-c" ]
          args:
            [
              "fastapi run xiaoxing_ai/main.py --host 0.0.0.0 --port 8000 --proxy-headers"
            ]
          ports:
            - containerPort: 8000
              protocol: TCP
          envFrom:
            - configMapRef:
                name: xiaoxing-ai-configmap
