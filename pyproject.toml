[project]
name = "xiaoxing-ai"
version = "0.1.0"
description = "Default template for PDM package"
authors = [{ "name" = "林筱越", "email" = "<EMAIL>" }]
requires-python = "==3.12.*"
readme = "README.md"
license = { text = "MIT" }
dependencies = [
    "fastapi[standard]>=0.115.6",
    "python-dotenv>=1.0.1",
    "langfuse>=2.56.0",
    "pydantic-settings>=2.6.1",
    "openai>=1.57.0",
    "sqlalchemy[asyncio]>=2.0.36",
    "elasticsearch<8",
    "ecs-logging>=2.2.0",
    "structlog>=24.4.0",
    "numpy>=2.2.0",
    "scikit-learn>=1.6.0",
    "opencv-python-headless>=*********",
    "asyncmy>=0.2.10",
    "python-Levenshtein>=0.27.1",
    "langchain>=0.2.12",
    "langchain-openai>=0.1.10",
    "langgraph>=0.5.4",
    "alibabacloud-ocr-api20210707>=3.1.3",
    "redis>=6.1.0",
    "Pillow>=11.2.1",
    "passlib[bcrypt]>=1.7.4",
    "pyjwt>=2.10.1",
    "oss2>=2.19.1",
    "beautifulsoup4>=4.13.4",
    "pymilvus>=2.5.11",
    "opentelemetry-exporter-otlp>=1.34.1",
    "opentelemetry-distro>=0.55b1",
    "opentelemetry-instrumentation-fastapi>=0.55b1",
    "opentelemetry-instrumentation-httpx>=0.55b1",
    "opentelemetry-instrumentation-logging>=0.55b1",
    "opentelemetry-instrumentation-sqlalchemy>=0.55b1",
    "opentelemetry-instrumentation-requests>=0.55b1",
    "opentelemetry-instrumentation-asyncio>=0.55b1",
    "opentelemetry-instrumentation-redis>=0.55b1",
    "opentelemetry-instrumentation-openai>=0.40.13",
    "diff-match-patch>=20241021",
]

[dependency-groups]
dev = [
    "pre-commit>=3.8.0",
    "pyaxmlparser>=0.3.31",
    "pyright>=1.1.390",
    "pytest>=8.3.2",
    "pytest-asyncio>=0.23.8",
    "pytest-xdist>=3.8.0",
    "ruff>=0.8.2",
    "sqlacodegen==3.0.0",
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/web/simple"
default = true

[tool.ruff]
# Allow lines to be as long as 120.
line-length = 120

[tool.ruff.lint]
select = [
    "E", # pycodestyle
    "F", # pyflakes
    "I", # isort
]

[tool.ruff.lint.per-file-ignores]
"xiaoxing_ai/*/templates.py" = ["E501"]
"xiaoxing_ai/*/prompts.py" = ["E501"]

[tool.ruff.format]
quote-style = "single"
indent-style = "space"

[tool.pyright]
include = ["xiaoxing_ai"]
exclude = [".venv", "**/migrations", "**/tests", "**/templates"]

[tool.pytest.ini_options]
addopts = "-ra -vv -n auto"
pythonpath = "."
asyncio_default_fixture_loop_scope = "function"
