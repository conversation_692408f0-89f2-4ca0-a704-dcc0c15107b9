from io import Bytes<PERSON>
from pathlib import Path
from typing import Optional

import structlog
from fastapi import UploadFile
from sqlalchemy import Select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from script import apk
from script.apk import Parsed<PERSON><PERSON>
from xiaoxing_ai.db import Async<PERSON>essionLocal
from xiaoxing_ai.db.models import AppVersion
from xiaoxing_ai.shared.oss import FileSizeError, oss

logger = structlog.get_logger('xiaoxing_ai')


def apk_to_uploadfile(apk_path: str) -> UploadFile:
    """将本地APK文件转换为UploadFile对象"""
    path = Path(apk_path)

    if path.suffix.lower() != '.apk':
        raise ValueError('File is not an APK file')

    try:
        with open(apk_path, 'rb') as f:
            file_content = f.read()

        return UploadFile(file=BytesIO(file_content), filename=path.name, size=len(file_content))
    except Exception as e:
        logger.error(f'读取APK文件失败，路径：{apk_path}，错误：{str(e)}')
        raise


async def check_existing_version(app: ParsedAPK, db: AsyncSession) -> Optional[AppVersion]:
    """检查版本是否已存在"""
    q_stmt = Select(AppVersion).where(
        AppVersion.version_code == app.version_code,
        AppVersion.package_name == app.package_name,
        AppVersion.version_name == app.version_name,
        AppVersion.md5 == app.md5,
    )

    try:
        qs = await db.execute(q_stmt)
        return qs.scalars().first()
    except SQLAlchemyError as e:
        logger.error(f'数据库查询失败：{str(e)}')
        raise


async def upload_app_file(data: bytes, app: ParsedAPK) -> tuple[str, Optional[str]]:
    """上传APK文件及其图标到OSS"""
    oss_path = f'{app.package_name}/{app.version_name}'

    try:
        # 上传APK文件
        url = oss.upload_data(
            data,
            prefix=f'math/{oss_path}',
            suffix='.apk',
        )

        # 上传图标（如果存在）
        icon_url = None
        if icon_buffer := app.icon_data:
            apk_icon = str(app.icon_info)
            icon_url = oss.upload_data(
                icon_buffer,
                prefix=f'math/{oss_path}',
                suffix=Path(apk_icon).suffix,
            )

        return url, icon_url

    except FileSizeError as e:
        logger.error(f'上传文件为空：{str(e)}')
        raise ValueError('上传文件为空')
    except Exception as e:
        logger.error(f'文件上传失败：{str(e)}')
        raise RuntimeError('文件上传失败')
    finally:
        apk.clean_cache(data)


async def save_app_version(
    db: AsyncSession,
    app: ParsedAPK,
    url: str,
    icon_url: Optional[str],
    description: str,
    is_forced: int,
    file_size: int,
) -> AppVersion:
    """保存应用版本信息到数据库"""
    try:
        obj = AppVersion(
            package_name=app.package_name,
            version_name=app.version_name,
            version_code=app.version_code,
            description=description,
            file_size=file_size,
            url=oss.cdn_url(url),
            icon=oss.cdn_url(icon_url) if icon_url else icon_url,
            is_forced=is_forced,
            md5=app.md5,
        )

        db.add(obj)
        await db.commit()
        return obj

    except SQLAlchemyError as e:
        logger.error(f'数据库操作失败：{str(e)}')
        await db.rollback()
        raise RuntimeError('数据库操作失败')


async def upload_app(is_forced: int, description: str, file: UploadFile) -> AppVersion:
    """上传应用主函数"""
    filename = file.filename or 'unknown_file'
    # 读取文件
    data = await file.read()

    # 解析APK
    try:
        app = apk.parse_apk(data)
    except Exception as e:
        logger.error(f'解析APK失败，文件名：{filename}，错误：{str(e)}')
        apk.clean_cache(data)
        raise ValueError(f'解析APK失败：{filename}')

    try:
        # 创建数据库连接
        async with AsyncSessionLocal() as db:
            # 检查版本是否存在
            if await check_existing_version(app, db):
                apk.clean_cache(data)
                raise ValueError(f'该应用版本已存在：{filename}')

            # 上传文件到OSS
            url, icon_url = await upload_app_file(data, app)

            # 保存应用版本信息
            return await save_app_version(
                db,
                app,
                url,
                icon_url,
                description,
                is_forced,
                file.size or 0,
            )

    except Exception as e:
        logger.error(f'应用上传失败，文件名：{filename}，错误：{str(e)}')
        raise


if __name__ == '__main__':
    import asyncio

    async def main():
        apk_path = r'C:\Users\<USER>\Downloads\AIMath_release_1.0.6-beta3.apk'
        try:
            f = apk_to_uploadfile(apk_path)
            result = await upload_app(
                0, '1.markdown支持$和$$\n2.解决搜题详情页数字人息屏后解锁崩溃问题（BUG830）和优化登录交互逻辑', f
            )
            print('上传成功:', result)
        except Exception as e:
            print(f'上传失败: {str(e)}')

    asyncio.run(main())
