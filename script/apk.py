import hashlib
from contextlib import contextmanager
from dataclasses import dataclass
from os import Path<PERSON><PERSON>
from pathlib import Path
from typing import Generator, TypeAlias, Union

from pyaxmlparser import APK

FileLike: TypeAlias = Union[PathLike[str], str]


@dataclass
class ParsedAPK:
    name: str
    version_code: int
    package_name: str
    version_name: str
    icon_data: bytes | None
    icon_info: str | None
    md5: str


@dataclass
class FileWithMD5:
    md5: str
    file: bytes


@contextmanager
def read_with_md5(path_or_data: FileLike | bytes, chunk_size: int = 8192) -> Generator[FileWithMD5, None, None]:
    if isinstance(path_or_data, bytes):
        yield FileWithMD5(md5=hashlib.md5(path_or_data).hexdigest(), file=path_or_data)
        return

    file_handle = open(path_or_data, 'rb')
    data = bytearray()
    try:
        md5_hash = hashlib.md5()
        while True:
            chunk = file_handle.read(chunk_size)
            if not chunk:
                break
            md5_hash.update(chunk)
            data.extend(chunk)

        md5_digest = md5_hash.hexdigest()
        file_handle.seek(0)  # 将文件指针重置到开头

        yield FileWithMD5(md5_digest, bytes(data))
    finally:
        file_handle.close()


def parse_apk(path_or_data: FileLike | bytes) -> ParsedAPK:
    with read_with_md5(path_or_data) as fs:
        apk: APK = APK(fs.file, raw=True)
        result = ParsedAPK(
            name=apk.get_app_name(),
            version_code=apk.version_code,
            package_name=apk.packagename,
            version_name=apk.version_name,
            icon_data=apk.icon_data,
            icon_info=apk.icon_info,
            md5=fs.md5,
        )
        # 取出需要的字段后释放内存
        apk.zip.close()
        return result


def clean_cache(path: FileLike | bytes) -> None:
    import gc

    gc.collect()

    if not isinstance(path, bytes):
        Path(path).unlink(missing_ok=True)
