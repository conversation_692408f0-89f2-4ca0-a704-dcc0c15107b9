from xiaoxing_ai.shared.xfyun import XFAuditService

if __name__ == '__main__':
    server = XFAuditService()
    import asyncio

    # resp = asyncio.run(server.create_blacklist("class_room黑名单","contraband"))
    # {
    #     'code': '000000',
    #     'desc': 'success',
    #     'data': {'lib_id': '46b8eed774a449578dd403a4294d1f5d'},
    #     'sid': 'dea2716a7b8c4bc6b6c217831a1f0820',
    # }

    # resp = asyncio.run(server.add_keyword('46b8eed774a449578dd403a4294d1f5d', ['毒品']))
    # {'code': '000000', 'desc': 'success', 'sid': 'a86ba5b2ce4d4ce7ab9abb5c597ac6f3'}

    # resp = asyncio.run(server.select_lib_detail('46b8eed774a449578dd403a4294d1f5d'))

    # 白名单
    # resp = asyncio.run(server.create_whitelist('class_room白名单'))
    # {
    #     'code': '000000',
    #     'desc': 'success',
    #     'data': {'lib_id': 'fafc317e2680468586b21f501c825be7'},
    #     'sid': '6c62900999c64eb294596878b06189c1',
    # }
    # resp = asyncio.run(server.add_white_keyword('fafc317e2680468586b21f501c825be7', ['习近平']))
    # {'code': '000000', 'desc': 'success', 'sid': 'd809655121eb41de8d991c0994c72502'}

    resp = asyncio.run(server.select_lib_detail('fafc317e2680468586b21f501c825be7'))

    print(resp)
