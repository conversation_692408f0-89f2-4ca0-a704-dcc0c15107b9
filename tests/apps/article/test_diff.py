from bs4 import BeautifulSoup

from xiaoxing_ai.apps.article.diff import get_diff
from xiaoxing_ai.apps.article.dto import DiffItem, DiffOut


def test_get_diff_same_text():
    """
    TC01: 相同文本返回空字典
    """
    result = get_diff('abc', 'abc')
    assert result == DiffOut(insert=[], delete=[], update=[])


def test_get_diff_insert():
    """
    TC02: 插入操作
    """
    result = get_diff('abc', 'abcXYZ')
    assert result.insert == [DiffItem(key='new', content='XYZ', index='1:3')]
    assert result.delete == []
    assert result.update == []


def test_get_diff_delete():
    """
    TC03: 删除操作
    """
    result = get_diff('abcXYZ', 'abc')
    assert result.delete == [DiffItem(key='old', content='XYZ', index='1:3')]
    assert result.insert == []
    assert result.update == []


def test_get_diff_replace():
    """
    TC04: 替换操作（删除+插入）
    """
    result = get_diff('abcXYZ', 'abcDEF')
    assert result.update == [
        [
            DiffItem(key='old', content='XYZ', index='1:3'),
            DiffItem(key='new', content='DEF', index='1:3'),
        ]
    ]
    assert result.delete == []
    assert result.insert == []


def test_get_diff_multiline():
    """
    TC05: 多行文本替换
    """
    old_text = 'a\nb\nc'
    new_text = 'a\nX\nY'

    result = get_diff(old_text, new_text)

    # b -> X 替换
    assert result.update[0][0].content == 'b\nc'
    assert result.update[0][0].index == '2:0'
    assert result.update[0][1].content == 'X\nY'
    assert result.update[0][1].index == '2:0'


def test_get_diff_empty_old():
    """
    测试空旧文本插入
    """
    result = get_diff('', 'new content')
    assert result.insert == [DiffItem(key='new', content='new content', index='1:0')]


def test_get_diff_empty_new():
    """
    测试空新文本删除
    """
    result = get_diff('old content', '')
    assert result.delete == [DiffItem(key='old', content='old content', index='1:0')]


def test_get_diff_mixed_changes():
    """
    测试混合修改：删除、插入、替换
    """
    old_text = 'Hello\nWorld\nThis is old'
    new_text = 'Hi\nWorld has changed\nThis is new'

    result = get_diff(old_text, new_text)

    # 替换 Hello -> Hi
    assert result.update[0][0].content == 'ello'
    assert result.update[0][0].index == '1:1'
    assert result.update[0][1].content == 'i'
    assert result.update[0][1].index == '1:1'

    # 替换 This is old -> This is new
    assert result.update[1][0].content == 'old'
    assert result.update[1][0].index == '3:8'
    assert result.update[1][1].content == 'new'
    assert result.update[1][1].index == '3:8'

    # 插入 has changed
    assert result.insert[0].content == ' has changed'
    assert result.insert[0].index == '2:5'


def test_diff_with_same_content():
    a = r"""23.已知a,b,c为正数,且满足$$  a b c = 1 $$.证明:(1)$$  \frac { 1 } { a } + \frac { 1 } { b } + \frac { 1 } { c } \leq a ^ { 2 } + b ^ { 2 } + c ^ { 2 } $$;(2)$$  ( a + b ) ^ { 3 } + ( b + c ) ^ { 3 } + ( c + a ) ^ { 3 } \geq 2 4 $$"""  # noqa: E501
    b = r"""23.已知a,b,c为正数,且满足$$  a b c = 1 $$.证明:(1)$$  \frac { 1 } { a } + \frac { 1 } { b } + \frac { 1 } { c } \leq a ^ { 2 } + b ^ { 2 } + c ^ { 2 } $$(2)$$  ( a + b ) ^ { 3 } + ( b + c ) ^ { 3 } + ( c + a ) ^ { 3 } \geq 2 4 $$"""  # noqa: E501

    result = get_diff(a, b)

    assert result.insert == []
    assert result.update == []


def test_diff_with_different_content():
    a = r"""23.已知a,b,c为正数,且满足$$  a b c = 1 $$.证明:(1)$$  1+1=2 $$;(2)$$  ( a + b ) ^ { 3 } + ( b + c ) ^ { 5 } + ( c + a ) ^ { 3 } \geq 2 4 $$"""  # noqa: E501
    b = r"""23.已知a,b,c为正数,且满足$$  a b c = 1 $$.证明:(1)$$  \frac { 1 } { a } + \frac { 1 } { b } + \frac { 1 } { c } \leq a ^ { 2 } + b ^ { 2 } + c ^ { 2 } $$(2)$$  ( a + b ) ^ { 3 } + ( b + c ) ^ { 3 } + ( c + a ) ^ { 3 } \geq 2 4 $$"""  # noqa: E501

    result = get_diff(a, b)

    assert result.insert == [DiffItem(key='new', content='} + b ^ { 2 } + c ^ { 2 } ', index='1:115')]
    assert len(result.update) != 0
    assert len(result.delete) == 1


def test_get_diff_html_output():
    """
    TC06: HTML 输出
    """
    result = get_diff('abcDEF', 'abcXYZ', is_html=True)
    assert 'style="background:#ffe6e6;">' in result  # 删除样式
    assert 'style="background:#e6ffe6;">' in result  # 插入样式


def test_diff_output_html():
    a = r"""23.已知a,b,c为正数,且满足$$  a b c = 1 $$.证明:(1)$$  1+1=2 $$;(2)$$  ( a + b ) ^ { 3 } + ( b + c ) ^ { 5 } + ( c + a ) ^ { 3 } \geq 2 4 $$"""  # noqa: E501
    b = r"""23.已知a,b,c为正数,且满足$$  a b c = 1 $$.证明:(1)$$  \frac { 1 } { a } + \frac { 1 } { b } + \frac { 1 } { c } \leq a ^ { 2 } + b ^ { 2 } + c ^ { 2 } $$(2)$$  ( a + b ) ^ { 3 } + ( b + c ) ^ { 3 } + ( c + a ) ^ { 3 } \geq 2 4 $$"""  # noqa: E501

    result = get_diff(a, b, is_html=True)

    soup = BeautifulSoup(result, 'html.parser')
    assert soup.find_all('ins')
    assert soup.find_all('del')
