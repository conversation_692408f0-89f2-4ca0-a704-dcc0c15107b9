import json
from unittest.mock import AsyncMock, patch

import pytest
from jinja2 import Template

from xiaoxing_ai.apps.article.constants import Grade, Level
from xiaoxing_ai.apps.article.prompts import PromptTemplates


# 模拟一个模板对象，它有render方法
class MockTemplate:
    def render(self, **kwargs):
        # 返回所有传入的参数
        params_str = ', '.join(f'{k}: {v}' for k, v in kwargs.items())
        return f'Rendered with params: {params_str}'


# 使用 patch 装饰器模拟 get_prompt_template 方法
@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_topic_analysis_prompt_success(mock_get_template):
    """
    测试 topic_analysis_prompt 成功获取模板的情况。
    """
    # 设置模拟方法的返回值
    mock_get_template.return_value = MockTemplate()

    result = await PromptTemplates.topic_analysis_prompt(topic='test_key', title='test_title', content='test_content')

    # 验证模拟方法被调用，并且参数正确
    mock_get_template.assert_called_once_with('topic_analysis')

    # 验证返回结果是否符合预期
    assert 'topic: test_key' in result
    assert 'title: test_title' in result
    assert 'content: test_content' in result


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_topic_analysis_prompt_template_not_found(mock_get_template):
    """
    测试 topic_analysis_prompt 找不到模板的情况，应返回空字符串。
    """
    mock_get_template.return_value = None

    result = await PromptTemplates.topic_analysis_prompt(topic='test_key', title='test_title', content='test_content')

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_gaozhong_content_eval_prompt(mock_get_template):
    """Tests the gaozhong_content_eval_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.gaozhong_content_eval_prompt(
        topic='test_key',
        content='test_content',
        analysis_data={'matching_level': 'value1'},
        title='test_title',
        word_count=Level.GAOZHONG.get_word_count(grade=None),
        max_score_limit=12,
        content_length=21,
        grade=None,
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('gaozhong_content_eval')

    # 可选：验证参数是否传递到结果中（根据MockTemplate实现）
    assert 'test_key' in result
    assert 'test_content' in result
    assert 'test_title' in result


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_gaozhong_content_eval_template_not_found(mock_get_template):
    """
    测试找不到模板的情况，应返回空字符串。
    """
    mock_get_template.return_value = None

    result = await PromptTemplates.gaozhong_content_eval_prompt(
        topic='test_key',
        content='test_content',
        analysis_data={'matching_level': 'value1'},
        title='test_title',
        word_count=Level.GAOZHONG.get_word_count(grade=None),
        max_score_limit=12,
        content_length=21,
        grade=None,
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_chuzhong_content_eval_prompt_success(mock_get_template):
    """Tests the chuzhong_content_eval_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.chuzhong_content_eval_prompt(
        topic='test_key',
        content='test_content',
        analysis_data={'matching_level': 'value1'},
        title='test_title',
        word_count=Level.CHUZHONG.get_word_count(grade=None),
        max_score_limit=12,
        content_length=21,
        grade=None,
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('chuzhong_content_eval')

    # 验证关键参数是否传递到结果中
    assert 'test_key' in result
    assert 'test_content' in result
    assert 'test_title' in result


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_chuzhong_content_eval_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    result = await PromptTemplates().chuzhong_content_eval_prompt(
        topic='test_key',
        content='test_content',
        analysis_data={'matching_level': 'value1'},
        title='test_title',
        word_count=Level.CHUZHONG.get_word_count(grade=None),
        max_score_limit=12,
        content_length=21,
        grade=None,
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_xiaoxue_content_eval_prompt_success(mock_get_template):
    """Tests the xiaoxue_content_eval_prompt method with grade-specific verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    grade = Grade.THREE  # 明确指定小学三年级
    result = await pt.xiaoxue_content_eval_prompt(
        topic='test_key',
        content='test_content',
        analysis_data={'matching_level': 'value1'},
        title='test_title',
        word_count=Level.XIAOXUE.get_word_count(grade),  # 使用小学阶段的word_count计算
        max_score_limit=123,
        content_length=321,
        grade=grade,  # 明确传入年级参数
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('xiaoxue_content_eval')

    # 验证关键参数是否传递到结果中
    assert 'test_key' in result
    assert 'test_content' in result
    assert 'test_title' in result
    # 可选：验证年级相关参数是否正确处理
    assert str(grade.value) in result  # 假设grade.value返回可识别的年级标识


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_xiaoxue_content_eval_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    grade = Grade.FOUR  # 测试不同年级的情况
    result = await PromptTemplates().xiaoxue_content_eval_prompt(
        topic='test_key',
        content='test_content',
        analysis_data={'matching_level': 'value1'},
        title='test_title',
        word_count=Level.XIAOXUE.get_word_count(grade),  # 使用小学阶段的word_count计算
        max_score_limit=123,
        content_length=321,
        grade=grade,  # 明确传入年级参数
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_deduction_prompt_success(mock_get_template):
    """Tests the deduction_prompt method with level-specific verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.deduction_prompt(
        content='test_content',
        title='test_title',
        content_length=21,
        level=Level.XIAOXUE,  # 明确指定小学级别
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('eval_deduction')

    # 验证关键参数是否传递到结果中
    assert 'test_content' in result
    assert 'test_title' in result


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_deduction_prompt_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    result = await PromptTemplates().deduction_prompt(
        content='test_content',
        title='test_title',
        content_length=21,
        level=Level.XIAOXUE,  # 明确指定小学级别
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_final_score_prompt(mock_get_template):
    """Tests the final_score_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.final_score_prompt(
        analysis_data={'matching_level': 'value1'},
        content_data={'paragraphs': 3, 'sentences': 10},
        deduction_data=[{'reason': 'typo', 'points': 1}],
        content_length=150,
        word_count=Level.CHUZHONG.get_word_count(Grade.THREE),
        level=Level.XIAOXUE,
        max_score_limit=100,
        grade=Grade.THREE,
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('final_score')

    # 可选：验证参数是否传递到结果中（根据MockTemplate实现）
    assert 'value1' in result  # analysis_data中的值
    assert 'typo' in result  # deduction_data中的值
    assert 'grade: 3' in result  # max_score_limit


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_final_score_prompt_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    result = await PromptTemplates().final_score_prompt(
        analysis_data={'matching_level': 'value1'},
        content_data={'paragraphs': 3, 'sentences': 10},
        deduction_data=[{'reason': 'typo', 'points': 1}],
        content_length=150,
        word_count=Level.CHUZHONG.get_word_count(Grade.THREE),
        level=Level.XIAOXUE,
        max_score_limit=100,
        grade=Grade.THREE,
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_analysis_prompt(mock_get_template):
    """Tests the improve_analysis_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.improve_analysis_prompt(
        topic='test_topic',
        title='test_title',
        content='test_content',
        ref_data={'key_points': ['value1', 'value2']},
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('improve_analysis')

    # 可选：验证参数是否传递到结果中
    assert 'test_topic' in result
    assert 'test_title' in result
    assert 'test_content' in result
    assert 'value1' in result  # ref_data中的值


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_analysis_prompt_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    result = await PromptTemplates().improve_analysis_prompt(
        topic='test_topic',
        title='test_title',
        content='test_content',
        ref_data={'key_points': ['value1', 'value2']},
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_content_prompt(mock_get_template):
    """Tests the improve_content_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.improve_content_prompt(
        topic='test_topic',
        title='test_title',
        content='test_content',
        ref_data={'key_points': ['value1', 'value2']},
        analysis_data={'key_points': ['value3', 'value4']},
        content_length=300,
        word_count=Level.CHUZHONG.get_word_count(Grade.THREE),
        level_score=None,
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('improve_content')

    # 可选：验证参数是否传递到结果中
    assert 'test_topic' in result
    assert 'test_title' in result
    assert 'test_content' in result
    assert 'value1' in result  # ref_data中的值
    assert 'value3' in result  # analysis_data中的值


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_content_prompt_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    result = await PromptTemplates().improve_content_prompt(
        topic='test_topic',
        title='test_title',
        content='test_content',
        ref_data={'key_points': ['value1', 'value2']},
        analysis_data={'key_points': ['value3', 'value4']},
        content_length=300,
        word_count=Level.CHUZHONG.get_word_count(Grade.THREE),
        level_score=None,
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_score_prompt(mock_get_template):
    """Tests the improve_score_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.improve_score_prompt(
        topic='test_topic',
        title='test_title',
        original_content='test_content',
        improved_content='test_improved_content',
        ref_data={'key_points': ['value1', 'value2']},
        analysis_data={'key_points': ['value3', 'value4']},
        level_score=100,
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('improve_score')

    # 可选：验证参数是否传递到结果中
    assert 'test_topic' in result
    assert 'test_title' in result
    assert 'test_content' in result
    assert 'test_improved_content' in result
    assert 'value1' in result  # ref_data中的值
    assert 'value3' in result  # analysis_data中的值
    assert '100' in result  # level_score


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_score_prompt_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    result = await PromptTemplates().improve_score_prompt(
        topic='test_topic',
        title='test_title',
        original_content='test_content',
        improved_content='test_improved_content',
        ref_data={'key_points': ['value1', 'value2']},
        analysis_data={'key_points': ['value3', 'value4']},
        level_score=100,
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_summary_prompt(mock_get_template):
    """Tests the improve_summary_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.improve_summary_prompt(
        title='test_title',
        original_content='test_content',
        improved_content='test_improved_content',
        analysis_data={'key_points': ['value3', 'value4']},
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('improve_summary')

    # 可选：验证参数是否传递到结果中
    assert 'test_title' in result
    assert 'test_content' in result
    assert 'test_improved_content' in result
    assert 'value3' in result  # analysis_data中的值


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_summary_prompt_template_not_found(mock_get_template):
    """
    测试找不到模板的情况，应返回空字符串。
    """
    mock_get_template.return_value = None

    result = await PromptTemplates().improve_summary_prompt(
        title='test_title',
        original_content='test_content',
        improved_content='test_improved_content',
        analysis_data={'key_points': ['value3', 'value4']},
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_prompt(mock_get_template):
    """Tests the improve_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    ref_data = {'key_points': ['value1', 'value2']}
    result = await pt.improve_prompt(
        topic='test_topic',
        content='test_content',
        content_length=120,
        cl_max=20,
        cl_normal=10,
        level_score=30,
        ref_data=json.dumps(ref_data),  # 保持json.dumps调用
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('improve')

    # 可选：验证参数是否传递到结果中
    assert 'test_topic' in result
    assert 'test_content' in result
    assert 'value1' in result  # ref_data中的值
    assert '20' in result  # cl_max
    assert '30' in result  # level_score


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_improve_prompt_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    ref_data = {'key_points': ['value1', 'value2']}
    result = await PromptTemplates().improve_prompt(
        topic='test_topic',
        content='test_content',
        content_length=120,
        cl_max=20,
        cl_normal=10,
        level_score=30,
        ref_data=json.dumps(ref_data),  # 保持json.dumps调用
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_compare_prompt(mock_get_template):
    """Tests the compare_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.compare_prompt(
        topic='test_topic',
        content='test_content',
        title='test_title',
        history_content='test_history_content',
        history_result='test_history_result',
        level=Level.XIAOXUE,
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('compare')

    # 可选：验证参数是否传递到结果中
    assert 'test_topic' in result
    assert 'test_content' in result
    assert 'test_title' in result
    assert 'test_history_content' in result
    assert 'test_history_result' in result


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_compare_prompt_template_not_found(mock_get_template):
    mock_get_template.return_value = None

    result = await PromptTemplates().compare_prompt(
        topic='test_topic',
        content='test_content',
        title='test_title',
        history_content='test_history_content',
        history_result='test_history_result',
        level=Level.XIAOXUE,
    )

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_ocr_system_prompt(mock_get_template):
    """Tests the ocr_system_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template

    pt = PromptTemplates()
    result = await pt.ocr_system_prompt()

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('ocr_system')

    # 可选：验证结果基本结构
    assert isinstance(result, str)  # 假设返回字符串


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_ocr_system_prompt_template_not_found(mock_get_template):
    """
    测试找不到模板的情况，应返回空字符串。
    """
    mock_get_template.return_value = None

    result = await PromptTemplates().ocr_system_prompt()

    assert result == ''


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_ai_qa_prompt(mock_get_template):
    """Tests the ai_qa_prompt method with basic verification."""

    # 设置模拟模板
    mock_template = MockTemplate()
    mock_get_template.return_value = mock_template
    mock_get_template.__class__ = Template

    pt = PromptTemplates()
    result = await pt.ai_qa_prompt(
        context='test_context',
        harmful='test_harmful',
        audit_description='test_audit_description',
    )

    # 基本验证
    assert result is not None
    mock_get_template.assert_called_once_with('ai_qa')


@patch('xiaoxing_ai.apps.article.prompts.PromptTemplates.get_prompt_template', new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_ai_qa_prompt_template_not_found(mock_get_template):
    """
    测试找不到模板的情况，应返回空字符串。
    """
    mock_get_template.return_value = None

    result = await PromptTemplates().ai_qa_prompt(
        context='test_context', harmful='test_harmful', audit_description='test_audit_description'
    )

    assert result == ''


def test_wordcount_feedback_prompt():
    """测试字数反馈提示的多种场景"""
    pt = PromptTemplates()

    # 测试用例数据：格式为 (当前长度, 最小长度, 最大长度, 预期关键词, 测试说明)
    test_cases = [
        # 正常场景
        (5, 10, 20, '不足', '字数不足时应提示增加内容'),
        (25, 10, 20, '超过', '字数超过时应提示删减内容'),
        (15, 10, 20, '符合', '字数合规时应通过反馈'),
        # 边界场景
        (9, 10, 20, '不足', '刚好低于最小值'),
        (21, 10, 20, '超过', '刚好高于最大值'),
        (10, 10, 20, '符合', '等于最小值'),
        (20, 10, 20, '符合', '等于最大值'),
        # 极端场景
        (0, 10, 20, '不足', '零字内容'),
        (100, 10, 20, '超过', '严重超过字数'),
        (15, 0, 100, '符合', '无限制范围'),
    ]

    for current_len, min_len, max_len, expected_keyword, _ in test_cases:
        result = pt.wordcount_feedback_prompt(current_length=current_len, min_length=min_len, max_length=max_len)

        # 精确断言
        if expected_keyword == '不足':
            assert '不足' in result, f"应包含'不足'提示: {result}"
            assert str(min_len) in result, '应显示最小字数要求'
        elif expected_keyword == '超过':
            assert '超过' in result, f"应包含'超过'提示: {result}"
            assert str(max_len) in result, '应显示最大字数要求'
        else:
            assert '符合' in result, '合规时应给出确认提示'
