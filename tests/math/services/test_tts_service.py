from typing import AsyncGenerator
from unittest.mock import AsyncMock, MagicMock

import pytest

from xiaoxing_ai.apps.math.repository import MathRepository
from xiaoxing_ai.apps.math.services import TTSService


@pytest.fixture
def mock_repo():
    """创建mock的MathRepository实例"""
    repo = MagicMock(spec=MathRepository)
    repo.get_model_prompt = AsyncMock(return_value=None)
    repo.get_ai_config_by_module = AsyncMock(return_value=None)
    return repo


@pytest.fixture
def tts_service(mock_repo):
    """创建TTSService实例，用于非LaTeX文本测试"""
    return TTSService(mock_repo, 'test_session_id')


@pytest.fixture
def tts_service_with_latex_mock(mock_repo):
    """创建带LaTeX转换mock的TTSService实例"""

    async def mock_speechify(text: str) -> AsyncGenerator[str, None]:
        # 模拟LaTeX转换：将LaTeX公式转换为口语化文本
        if '$x^2$' in text:
            yield 'x的平方'
        elif '$$\\frac{1}{2}$$' in text:
            yield '二分之一'
        else:
            yield f'转换后的文本：{text}'

    service = TTSService(mock_repo, 'test_session_id')
    service.speechify = mock_speechify
    return service


class TestBasicFunctionality:
    """基础功能测试"""

    @pytest.mark.parametrize(
        'text, expected',
        [
            ('$x^2$', True),  # 行内公式
            ('$$\\frac{1}{2}$$', True),  # 块公式
            ('\\(a + b\\)', True),  # 括号公式
            ('普通文本', False),  # 无LaTeX
            ('数字1.23', False),  # 包含点但不属于LaTeX
            ('', False),  # 空文本
        ],
    )
    def test_has_latex(self, tts_service, text, expected):
        """测试LaTeX公式检测功能"""
        assert tts_service._has_latex(text) == expected


class TestTextProcessing:
    """文本处理测试"""

    @pytest.mark.asyncio
    async def test_complete_sentences_processing(self, tts_service):
        """测试完整句子的处理 - 应该立即生成事件"""
        text = '你好。请问？如何！'
        events = []

        async for event in tts_service.truncate_for_tts(text):
            events.append(event)

        # 验证生成三个句子的事件
        assert len(events) == 3
        assert events[0]['content'] == '你好。'
        assert events[0]['thread_id'] == 'test_session_id'
        assert events[1]['content'] == '请问？'
        assert events[2]['content'] == '如何！'

    @pytest.mark.asyncio
    async def test_incomplete_text_accumulation(self, tts_service):
        """测试不完整文本的累积"""
        # 第一次调用：有标点的完整句子
        events1 = []
        async for event in tts_service.truncate_for_tts('第一部分。'):
            events1.append(event)

        assert len(events1) == 1
        assert events1[0]['content'] == '第一部分。'

        # 第二次调用：无标点文本，应该被累积
        events2 = []
        async for event in tts_service.truncate_for_tts('第二部分'):
            events2.append(event)

        assert len(events2) == 0  # 无事件生成
        assert tts_service._accumulated_text == '第二部分'

    @pytest.mark.asyncio
    async def test_final_chunk_processing_with_flush(self, tts_service):
        """测试使用flush方法强制处理累积文本"""
        # 先累积一些文本
        async for _ in tts_service.truncate_for_tts('累积的文本'):
            pass

        assert tts_service._accumulated_text == '累积的文本'

        # 使用flush强制处理
        events = []
        async for event in tts_service.flush():
            events.append(event)

        assert len(events) == 1
        assert events[0]['content'] == '累积的文本。'
        assert tts_service._accumulated_text == ''  # 应该被清空

    @pytest.mark.asyncio
    async def test_empty_text_handling(self, tts_service):
        """测试空文本处理"""
        events = []
        async for event in tts_service.truncate_for_tts(''):
            events.append(event)

        assert len(events) == 0
        assert tts_service._accumulated_text == ''

    @pytest.mark.asyncio
    async def test_mixed_complete_and_incomplete_text(self, tts_service):
        """测试混合完整和不完整文本"""
        # 输入：完整句子 + 不完整文本
        text = '完整句子。不完整文本'
        events = []

        async for event in tts_service.truncate_for_tts(text):
            events.append(event)

        # 应该只处理完整句子
        assert len(events) == 1
        assert events[0]['content'] == '完整句子。'
        assert tts_service._accumulated_text == '不完整文本'


class TestLatexProcessing:
    """LaTeX处理测试"""

    @pytest.mark.asyncio
    async def test_latex_content_conversion(self, tts_service_with_latex_mock):
        """测试LaTeX内容转换（不是跳过）"""
        events = []
        async for event in tts_service_with_latex_mock.truncate_for_tts('$x^2$。'):
            events.append(event)

        # LaTeX内容应该被转换，不是跳过
        assert len(events) == 1
        assert events[0]['content'] == 'x的平方'
        assert events[0]['thread_id'] == 'test_session_id'

    @pytest.mark.asyncio
    async def test_mixed_latex_and_normal_text(self, tts_service_with_latex_mock):
        """测试混合LaTeX和普通文本"""
        # 普通文本：直接输出
        events1 = []
        async for event in tts_service_with_latex_mock.truncate_for_tts('普通文本。'):
            events1.append(event)

        assert len(events1) == 1
        assert events1[0]['content'] == '普通文本。'

        # LaTeX文本：转换后输出
        events2 = []
        async for event in tts_service_with_latex_mock.truncate_for_tts('$$\\frac{1}{2}$$。'):
            events2.append(event)

        assert len(events2) == 1
        assert events2[0]['content'] == '二分之一'


class TestFlushFunctionality:
    """测试flush功能"""

    @pytest.mark.asyncio
    async def test_flush_accumulated_text(self, tts_service):
        """测试flush方法清空累积文本"""
        # 先累积一些文本
        async for _ in tts_service.truncate_for_tts('累积的文本'):
            pass

        assert tts_service._accumulated_text == '累积的文本'

        # 调用flush
        events = []
        async for event in tts_service.flush():
            events.append(event)

        assert len(events) == 1
        assert events[0]['content'] == '累积的文本。'
        assert events[0]['thread_id'] == 'test_session_id'
        assert tts_service._accumulated_text == ''  # 应该被清空

    @pytest.mark.asyncio
    async def test_flush_empty_text(self, tts_service):
        """测试flush空文本"""
        events = []
        async for event in tts_service.flush():
            events.append(event)

        assert len(events) == 0
        assert tts_service._accumulated_text == ''
