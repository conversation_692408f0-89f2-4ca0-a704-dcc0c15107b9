from xiaoxing_ai.shared.helper import calculate_similarity


class TestCalculateSimilarity:
    def test_empty_strings(self):
        assert calculate_similarity('', '') == 0.0
        assert calculate_similarity('', 'hello') == 0.0
        assert calculate_similarity('hello', '') == 0.0

    def test_identical_strings(self):
        assert calculate_similarity('hello', 'hello') == 1.0
        assert calculate_similarity('world', 'world') == 1.0

    def test_similar_strings(self):
        assert calculate_similarity('hello', 'hellow') > 0.5
        assert calculate_similarity('world', 'worlld') > 0.5

    def test_completely_different_strings(self):
        assert calculate_similarity('hello', 'world') < 0.5
        assert calculate_similarity('abc', 'def') < 0.5

    def test_strings_with_different_lengths(self):
        assert calculate_similarity('hello', 'hello world') < 1.0
        assert calculate_similarity('abc', 'abcdef') < 1.0
