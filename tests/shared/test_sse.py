from dataclasses import dataclass
from unittest.mock import Mock

from xiaoxing_ai.shared.sse import _is_pydantic_model, _serialize_data, make_sse_event


@dataclass
class TestDataClass:
    field1: str
    field2: int
    field3: bool = False


class TestSSEEventGeneration:
    def test_basic_event(self):
        result = make_sse_event({'key': 'value'}, event_type='test')
        assert 'event: test' in result
        assert 'data: {"key": "value"}' in result
        assert result.endswith('\n\n')

    def test_event_with_id(self):
        result = make_sse_event({'key': 'value'}, event_type='test', event_id='123')
        assert 'id: 123' in result
        assert 'event: test' in result
        assert 'data: {"key": "value"}' in result

    def test_event_with_retry(self):
        result = make_sse_event({'key': 'value'}, event_type='test', retry=1000)
        assert 'retry: 1000' in result
        assert 'event: test' in result
        assert 'data: {"key": "value"}' in result

    def test_string_data(self):
        result = make_sse_event('plain string', event_type='test')
        assert 'data: plain string' in result

    def test_json_string_data(self):
        json_str = '{"pre": "serialized"}'
        result = make_sse_event(json_str, event_type='test')
        assert 'data: {"pre": "serialized"}' in result

    def test_dataclass_event_data(self):
        test_obj = TestDataClass(field1='test', field2=123)
        result = make_sse_event(test_obj, event_type='dataclass')
        assert 'event: dataclass' in result
        assert 'data: {"field1": "test", "field2": 123, "field3": false}' in result

    def test_event_without_type(self):
        """Test that event_type is optional and no event field is included when not provided"""
        result = make_sse_event({'key': 'value'})
        assert 'event:' not in result
        assert 'data: {"key": "value"}' in result
        assert result.endswith('\n\n')

    def test_event_without_type_with_id_and_retry(self):
        """Test that other optional parameters work when event_type is not provided"""
        result = make_sse_event({'key': 'value'}, event_id='123', retry=1000)
        assert 'id: 123' in result
        assert 'retry: 1000' in result
        assert 'event:' not in result
        assert 'data: {"key": "value"}' in result

    def test_data_only_string(self):
        """Test simple string data without event type"""
        result = make_sse_event('Hello World')
        assert result == 'data: Hello World\n\n'

    def test_data_only_dict(self):
        """Test dict data without event type"""
        result = make_sse_event({'message': 'hello'})
        assert result == 'data: {"message": "hello"}\n\n'

    def test_data_json_string(self):
        json_str = '{"message": "hello"}'
        result = make_sse_event(json_str)
        assert result == 'data: {"message": "hello"}\n\n'


class TestDataSerialization:
    def test_dict_serialization(self):
        assert _serialize_data({'key': 'value'}) == '{"key": "value"}'

    def test_string_serialization(self):
        assert _serialize_data('plain string') == 'plain string'

    def test_json_string_serialization(self):
        json_str = '{"pre": "serialized"}'
        assert _serialize_data(json_str) == json_str

    def test_pydantic_model_serialization(self, monkeypatch):
        mock_model = Mock()
        mock_model.model_dump_json.return_value = '{"model": "data"}'

        monkeypatch.setattr('xiaoxing_ai.shared.sse._is_pydantic_model', lambda x: True)

        assert _serialize_data(mock_model) == '{"model": "data"}'

    def test_object_with_dict_serialization(self):
        class TestObject:
            def __init__(self):
                self.key = 'value'

        assert _serialize_data(TestObject()) == '{"key": "value"}'

    def test_dataclass_serialization(self):
        test_obj = TestDataClass(field1='test', field2=123)
        assert _serialize_data(test_obj) == '{"field1": "test", "field2": 123, "field3": false}'

    def test_dataclass_with_defaults_serialization(self):
        test_obj = TestDataClass(field1='test', field2=123, field3=True)
        assert _serialize_data(test_obj) == '{"field1": "test", "field2": 123, "field3": true}'


class TestPydanticDetection:
    def test_pydantic_detection(self):
        from pydantic import BaseModel

        class MockModel(BaseModel):
            pass

        assert _is_pydantic_model(MockModel()) is True

    def test_non_pydantic_detection(self):
        class NonPydantic:
            pass

        assert _is_pydantic_model(NonPydantic()) is False

    def test_subclass_detection(self):
        from pydantic import BaseModel

        class BaseMockModel(BaseModel):
            pass

        class SubMockModel(BaseMockModel):
            pass

        assert _is_pydantic_model(SubMockModel()) is True
