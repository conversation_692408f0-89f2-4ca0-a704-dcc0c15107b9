from fastapi import APIRouter, Depends

from xiaoxing_ai.apps.article import get_headers
from xiaoxing_ai.apps.article.views import router as article_router
from xiaoxing_ai.apps.article.views.v2 import router_v2 as article_router_v2
from xiaoxing_ai.apps.civil_servant.views import router as civil_servant_router
from xiaoxing_ai.apps.classroom.views import router as classroom_router
from xiaoxing_ai.apps.mandala.views import router as mandala_router
from xiaoxing_ai.apps.math.views import router as math_router

__all__ = ['api_v1', 'api_v2']

api_v1 = APIRouter(prefix='/v1', tags=['v1'])
api_v2 = APIRouter(prefix='/v2', tags=['v2'])

api_v1.include_router(article_router, dependencies=[Depends(get_headers)])
api_v1.include_router(mandala_router)
api_v1.include_router(classroom_router)
api_v1.include_router(civil_servant_router)
api_v1.include_router(math_router)


api_v2.include_router(article_router_v2, dependencies=[Depends(get_headers)])
