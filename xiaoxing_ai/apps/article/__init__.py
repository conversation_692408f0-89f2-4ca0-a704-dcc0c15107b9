from typing import Annotated

from fastapi import Depends, Header
from pydantic import BaseModel, Field


class HeaderState(BaseModel):
    sn: str | None = Field(None, description='设备序列号')
    ip: str | None = Field(None, description='ip地址')
    user_id: str | None = Field(None, description='用户id')


async def get_headers(
    sn: str | None = Header(None, alias='X-Sn', description='设备序列号'),
    ip: str | None = Header(None, alias='X-Ip', description='ip地址'),
    user_id: str | None = Header(None, alias='X-User-Id', description='用户id'),
) -> HeaderState:
    """请求头"""

    return HeaderState(
        sn=sn,
        ip=ip,
        user_id=user_id,
    )


HeaderDep = Annotated[HeaderState, Depends(get_headers)]
