import structlog
from langfuse.decorators import observe
from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionSystemMessageParam

from xiaoxing_ai.shared.helper import extract_json
from xiaoxing_ai.shared.llms import llm_chat

from ..constants import Level
from ..dto import (
    ChuzhongReviewSchema,
    GaozhongReviewSchema,
    ReviewOut,
    XiaoxueReviewSchema,
)
from ..prompts import PromptTemplates
from .react_agents import ReactEvaluator
from .tools import FUNCTION_SCHEMA

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class EnhancedEvaluator:
    """增强型作文评分器，使用ReAct方式实现更精准的作文评分"""

    def __init__(self):
        """初始化评分器"""

        self.react_evaluator = ReactEvaluator()

    @observe(capture_output=False)
    async def enhanced_review(self, llm, model, topic, title, content, level, grade, stream=False):
        """
        增强型作文评分

        Args:
            llm: 大模型实例
            model: 模型名称
            topic: 作文标题
            title: 标题
            content: 作文内容
            level: 作文等级
            grade: 年级
            uid: 唯一标识
            stream: 是否流式输出

        Returns:
            dict: 评分结果
        """
        # 为了保持兼容性，需要确保调用 final_score 步骤时开启function calling
        return await self.react_evaluator.evaluate(
            llm=llm,
            model=model,
            topic=topic,
            title=title,
            content=content,
            level=level,
            grade=grade,
            stream=stream,
            tools=[{'type': 'function', 'function': FUNCTION_SCHEMA}],  # 添加function schema 支持
        )

    @observe(capture_output=False)
    async def compare_review(
        self,
        llm: AsyncOpenAI,
        model: str,
        level: Level,
        topic: str,
        title: str,
        content: str,
        history_content: str,
        history_result: dict,
    ) -> dict:
        prompt = await PromptTemplates.compare_prompt(
            level=level,
            topic=topic,
            title=title,
            content=content,
            history_content=history_content,
            history_result=history_result,
        )
        if not prompt or not isinstance(prompt, str):
            raise ValueError('提示词无效')

        params = {
            'model': model,
            'temperature': 0.7,
            'messages': [
                ChatCompletionSystemMessageParam(content=prompt, role='system'),
            ],
            'response_format': {'type': 'json_object'},
        }
        res_data = await llm_chat(llm, params=params)
        res = extract_json(res_data)

        return res

    @staticmethod
    def format_review_result(resp, level, uid) -> ReviewOut:
        """
        格式化评分结果为固定的格式

        Args:
            resp (dict): 评分结果
            level (Level): 作文等级
            uid (str): 唯一标识

        Returns:
            ReviewOut: 格式化后的结果
        """
        res = resp.copy()
        detail_data = res.pop('detail')
        match level:
            case level.XIAOXUE:
                schema = XiaoxueReviewSchema
            case level.CHUZHONG:
                schema = ChuzhongReviewSchema
            case level.GAOZHONG:
                schema = GaozhongReviewSchema
            case _:
                raise ValueError(f'不支持的作文等级: {level}')
        try:
            detail = schema(**detail_data)
            result = ReviewOut(**res, review_id=uid, detail=detail)

        except Exception as e:
            logger.error(f'格式化评分结果失败: {e}', llm_resp=resp)
            return ReviewOut(
                review_id=uid,
                total_score=0,
                level='评分失败',
                summary='评分过程出现错误，请重试',
                detail=schema(),  # type: ignore
            )

        return result
