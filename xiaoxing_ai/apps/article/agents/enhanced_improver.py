import structlog
from langfuse.decorators import langfuse_context, observe

from .react_agents import ReactImprover

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class EnhancedImprover:
    def __init__(self):
        self.react_improver = ReactImprover()

    @observe(capture_output=False)
    async def enhanced_improve(
        self, *, llm, model, topic, title, content, ref_data, level, grade=None, stream=False, **kwargs
    ):
        """统一的作文优化入口，使用ReAct方式进行优化

        四个步骤:
        1. 分析改进点 - 根据原文和评分数据分析需要改进的方面
        2. 生成优化内容 - 根据分析生成优化后的作文内容
        3. 预估分数 - 对优化后的作文进行评分预估
        4. 生成总结 - 对比原文和优化后文章，总结改进之处
        """
        # 记录跟踪会话ID
        langfuse_context.update_current_trace(session_id=kwargs.get('uid', None))

        # 使用React优化器
        result = await self.react_improver.improve(
            llm=llm,
            model=model,
            topic=topic,
            title=title,
            content=content,
            ref_data=ref_data,
            level=level,
            grade=grade,
            stream=stream,
            **kwargs,
        )  # type: ignore

        return result
