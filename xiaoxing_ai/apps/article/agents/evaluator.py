import json
import re
from copy import deepcopy

import structlog
from fastapi import HTT<PERSON>Exception
from langfuse.decorators import langfuse_context, observe
from openai import AsyncOpenAI
from openai.types.chat import (
    ChatCompletionChunk,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from xiaoxing_ai.shared.helper import extract_json, remove_blank_characters
from xiaoxing_ai.shared.llms import llm_chat, llm_chat_stream

from ..constants import Grade, Level
from ..prompts import PromptTemplates
from ..templates import (
    CHUZHONG_FRAGMENTS_OUT,
    CHUZHONG_OUT,
    CHUZHONG_SYSTEM_TEMPLATE,
    GAOZHONG_FRAGMENTS_OUT,
    GAOZHONG_OUT,
    GAOZHONG_SYSTEM_TEMPLATE,
    XIAOXUE_FRAGMENTS_OUT,
    XIAOXUE_OUT,
    XIAOXUE_SYSTEM_TEMPLATE,
)

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class Evaluator:
    def __init__(self):
        self.mapping = {
            Level.XIAOXUE: (XIAOXUE_SYSTEM_TEMPLATE, XIAOXUE_OUT, XIAOXUE_FRAGMENTS_OUT),
            Level.CHUZHONG: (CHUZHONG_SYSTEM_TEMPLATE, CHUZHONG_OUT, CHUZHONG_FRAGMENTS_OUT),
            Level.GAOZHONG: (GAOZHONG_SYSTEM_TEMPLATE, GAOZHONG_OUT, GAOZHONG_FRAGMENTS_OUT),
        }

    @observe(capture_output=False)
    async def review(
        self,
        *,
        llm: AsyncOpenAI,
        model: str,
        topic: str,
        content: str,
        level: Level = Level.GAOZHONG,
        grade: Grade | None = None,
        **kwargs,
    ):
        langfuse_context.update_current_trace(session_id=kwargs.get('uid', None))
        system_template, out_template, fragments_out_template = self.mapping[level]
        word_count = level.get_word_count(grade)
        content_length = len(content)
        prompt = await PromptTemplates.review_prompt(
            topic=topic,
            content=content,
            content_length=content_length,
            out_template=fragments_out_template if content_length < word_count.approximate else out_template,
            approximate=word_count.approximate,
            normal=word_count.normal,
        )
        if not prompt or not isinstance(prompt, str):
            raise ValueError('提示词无效')

        params = {
            'model': model,
            'temperature': 0.6,
            'messages': [
                ChatCompletionSystemMessageParam(content=system_template, role='system'),
                ChatCompletionUserMessageParam(content=prompt, role='user'),
            ],
            'response_format': {'type': 'json_object'},
        }
        res_data = await llm_chat(llm, params)
        res = extract_json(res_data)

        return res

    @observe(capture_output=False)
    async def improve(
        self,
        *,
        llm: AsyncOpenAI,
        model: str,
        topic: str,
        content: str,
        ref_data: dict,
        level: Level = Level.GAOZHONG,
        grade: Grade | None = None,
        stream: bool = False,
        retry: int = 3,
    ):
        if retry == 0:
            raise HTTPException(status_code=400, detail='作文优化失败')

        word_count = level.get_word_count(grade)
        # 判断去除字符后的长度
        new_content = remove_blank_characters(content)
        length = len(new_content)
        if length >= 1000:
            cl_max = length
        else:
            cl_max = word_count.max

        prompt = await PromptTemplates.improve_prompt(
            topic=topic,
            content=content,
            content_length=length,
            cl_normal=word_count.normal + 100,
            cl_max=cl_max,
            level_score=level.get_score(),
            ref_data=ref_data,
        )
        if not prompt or not isinstance(prompt, str):
            raise ValueError('提示词无效')

        params = {
            'model': model,
            'temperature': 0.7,
            'messages': [
                ChatCompletionUserMessageParam(content=prompt, role='user'),
            ],
            'stream': stream,
            'response_format': {'type': 'json_object'},
        }

        buffer = []
        chunk_temp: ChatCompletionChunk | None = None
        if stream:
            resp = await llm_chat_stream(llm, params)
            async for chunk in resp:
                # azure_openai 第一个可能为空数组
                if not chunk or not chunk.choices:
                    continue

                if chunk_temp is None:
                    chunk_temp = chunk

                if cc := chunk.choices[0].delta.content:
                    buffer.append(cc)

            result = ''.join(buffer)
            # 替换掉单个换行符（但不是\n\n）
            text = re.sub(r'(?<!\n)\n(?!\n)', '', result)
            # 去除所有空格
            text = text.replace(' ', '')
            res = json.loads(text)
            # res = extract_json(result)
            res_content = res['content']
        else:
            res_content = await llm_chat(llm, params)
            res = extract_json(res_content)

        # 增加对字数的检查
        terse_content = remove_blank_characters(res_content)
        if len(terse_content) < word_count.normal:
            await logger.ainfo('字数不足进行重试!', content_len=len(terse_content))
            return await self.improve(  # type: ignore
                llm=llm,
                model=model,
                topic=topic,
                content=terse_content,
                ref_data=ref_data,
                level=level,
                grade=grade,
                stream=stream,
                retry=retry - 1,
            )

        if stream:
            # 将 chunk_list 转换为生成器并返回
            async def buffer_generator(_result: dict):
                # 先将分数返回
                yield _result['total_score']

                for item in _result['content']:
                    _chunk = deepcopy(chunk_temp)
                    _chunk.choices[0].delta.content = item  # type: ignore
                    yield _chunk

            return buffer_generator(res)

        return res

    @observe(capture_output=False)
    async def summary(
        self,
        *,
        llm: AsyncOpenAI,
        model: str,
        content: str,
        improve_content: str,
        title: str,
        analysis_data: dict,
        stream: bool = False,
        temperature: float = 0.4,
    ):
        """
        生成优化总结
        """
        # 生成总结提示词

        summary_prompt = await PromptTemplates.improve_summary_prompt(content, improve_content, title, analysis_data)
        if not summary_prompt or not isinstance(summary_prompt, str):
            raise ValueError('提示词无效')

        params = {
            'model': model,
            'temperature': temperature,
            'messages': [
                ChatCompletionUserMessageParam(content=summary_prompt, role='user'),
            ],
            'stream': stream,
        }

        if stream:
            return await llm_chat_stream(llm, params)
        else:
            return await llm_chat(llm, params)
