from openai.types.chat import ChatCompletionUserMessageParam

from xiaoxing_ai.shared.helper import extract_json
from xiaoxing_ai.shared.llms import ModelName, azure_openai, llm_chat, llm_chat_stream

from ..dto.homework import EvaluationIn, ImprovedIn


class Evaluator:
    @staticmethod
    async def get_evaluation(
        template: str,
        data: EvaluationIn,
    ):
        prompt = template.format(
            level=data.level.value,
            course_title=data.course_title,
            course_key_points=data.course_key_points,
            require=data.require,
            word_count=data.word_count,
            min_word_count=min(data.word_count),
            course_prompt=data.elements.course_prompt,
            course_object=data.elements.course_object,
            course_core_tasks=data.elements.course_core_tasks,
            framework=data.elements.framework,
            core_tasks=data.elements.core_tasks,
            content=data.content,
            content_length=len(data.content),
        )

        params = {
            'model': ModelName.GPT_4O,
            'temperature': 0.7,
            'messages': [
                ChatCompletionUserMessageParam(content=prompt, role='user'),
            ],
            'response_format': {'type': 'json_object'},
        }
        res_data = await llm_chat(azure_openai, params)
        res = extract_json(res_data)

        return res

    @staticmethod
    async def get_improved(template: str, data: ImprovedIn, result: dict):
        prompt = template.format(
            course_title=data.course_title,
            course_key_points=data.course_key_points,
            require=data.require,
            word_count=data.word_count,
            course_prompt=data.elements.course_prompt,
            course_object=data.elements.course_object,
            course_core_tasks=data.elements.course_core_tasks,
            framework=data.elements.framework,
            core_tasks=data.elements.core_tasks,
            content=data.content,
            content_length=len(data.content),
            result=result,
        )

        params = {
            'model': ModelName.GPT_4O,
            'temperature': 0.7,
            'messages': [
                ChatCompletionUserMessageParam(content=prompt, role='user'),
            ],
            'stream': True,
        }

        return await llm_chat_stream(azure_openai, params)
