import json
import math
from dataclasses import dataclass
from typing import Any, As<PERSON><PERSON>enerator, Dict

import structlog
from langfuse.decorators import langfuse_context, observe
from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionSystemMessageParam

from xiaoxing_ai.shared.helper import count_effective_chars, extract_json, remove_blank_characters
from xiaoxing_ai.shared.llms import llm_chat

from ..constants import Grade, Level
from ..prompts import PromptTemplates
from .tools import ScoreCalculator

logger = structlog.stdlib.get_logger('xiaoxing_ai')


@dataclass
class EvaluationConfig:
    """评分配置类"""

    level: Level
    max_score: int  # 正常最高分
    limit_score: int  # 偏题最高分
    content_eval_prompt: str  # 内容评分prompt模板名称


class ReactEvaluator:
    def __init__(self):
        self._configs = {
            Level.XIAOXUE: EvaluationConfig(
                level=Level.XIAOXUE, max_score=30, limit_score=18, content_eval_prompt='xiaoxue_content_eval_prompt'
            ),
            Level.CHUZHONG: EvaluationConfig(
                level=Level.CHUZHONG, max_score=60, limit_score=32, content_eval_prompt='chuzhong_content_eval_prompt'
            ),
            Level.GAOZHONG: EvaluationConfig(
                level=Level.GAOZHONG, max_score=60, limit_score=30, content_eval_prompt='gaozhong_content_eval_prompt'
            ),
        }

    @observe(capture_output=False)
    async def _analyze_topic(self, llm, model, topic, title, content):
        """题干分析"""
        analysis_prompt = await PromptTemplates.topic_analysis_prompt(topic, title, content)
        analysis_result = await self._llm_request(llm, model, analysis_prompt, temperature=0.3)
        return extract_json(analysis_result)

    @observe(capture_output=False)
    async def _check_deductions(self, llm, model, content, title, content_length, word_count, level):
        """扣分项检查"""
        deduction_prompt = await PromptTemplates.deduction_prompt(content, title, content_length, level)
        deduction_result = await self._llm_request(llm, model, deduction_prompt, temperature=0.2)
        data = extract_json(deduction_result)

        # 作文字数大于正常字数或者小于一半时, 不扣字数分
        if content_length > word_count.normal or content_length < word_count.approximate:
            return data

        cl_diff = math.ceil((word_count.normal - content_length) / 50)
        if level == Level.XIAOXUE:
            score = cl_diff * 1
        else:
            score = cl_diff * 3

        if cl_diff > 0:
            data['word_count_deduction'] = score
            # 重新计算总分
            data['total_deduction'] = data['typo_deduction'] + data['punctuation_deduction'] + score

        langfuse_context.update_current_observation(output=data)

        return data

    @observe(capture_output=False)
    async def _evaluate_content(
        self,
        llm,
        model,
        config,
        topic,
        title,
        content,
        analysis_data,
        max_score_limit,
        content_length,
        word_count,
        grade,
    ):
        """内容评分"""
        prompt_method = getattr(PromptTemplates, config.content_eval_prompt)  # 获取prompt内置方法
        content_prompt = await prompt_method(
            topic,
            content,
            analysis_data,
            title,
            max_score_limit,
            content_length,
            word_count,
            grade if config.level == Level.XIAOXUE else None,
        )
        content_result = await self._llm_request(llm, model, content_prompt, temperature=0.4)
        return extract_json(content_result)

    @observe(capture_output=False)
    async def _get_final_score(
        self,
        llm,
        model,
        analysis_data,
        content_data,
        deduction_data,
        content_length,
        word_count,
        level,
        max_score_limit,
        grade=None,
        tools=None,
    ):
        """最终评分"""
        final_prompt = await PromptTemplates.final_score_prompt(
            analysis_data, content_data, deduction_data, content_length, word_count, level, max_score_limit, grade
        )

        # 如果提供了tools，使用function calling
        if tools:
            final_result = await self._llm_request_with_tools(llm, model, final_prompt, temperature=0.5, tools=tools)
        else:
            final_result = await self._llm_request(llm, model, final_prompt, temperature=0.5)

        return extract_json(final_result)

    @staticmethod
    async def _llm_request(llm, model, prompt, temperature):
        """统一的LLM请求处理"""
        return await llm_chat(
            llm,
            {
                'model': model,
                'temperature': temperature,
                'messages': [
                    ChatCompletionSystemMessageParam(content=prompt, role='system'),
                ],
                'response_format': {'type': 'json_object'},
            },
        )

    @staticmethod
    @observe(capture_output=False)
    async def _llm_request_with_tools(llm, model, prompt, temperature, tools):
        """支持function calling的LLM请求处理"""
        try:
            response = await llm.chat.completions.create(
                model=model,
                temperature=temperature,
                messages=[
                    {'role': 'system', 'content': prompt},
                ],
                tools=tools,
                tool_choice='auto',
            )

            # 处理工具调用
            message = response.choices[0].message

            if message.tool_calls:
                # 提取工具调用结果
                tool_call = message.tool_calls[0]
                function_name = tool_call.function.name
                function_args = json.loads(tool_call.function.arguments)

                # 只处理calculate_article_score函数调用
                if function_name == 'calculate_article_score':
                    # 实际调用本地计算函数

                    # 根据参数动态设置最大分数
                    max_score_limit = function_args.get('max_score_limit')

                    result = ScoreCalculator.calculate_final_score(
                        content_data=function_args.get('content_data'),
                        deduction_data=function_args.get('deduction_data'),
                        level_grade=function_args.get('level_grade'),
                        max_score_limit=max_score_limit,
                    )

                    # 给模型回传结果，获取完整的评价内容
                    second_response = await llm.chat.completions.create(
                        model=model,
                        temperature=temperature,
                        messages=[
                            {'role': 'system', 'content': prompt},
                            message,
                            {
                                'role': 'tool',
                                'tool_call_id': tool_call.id,
                                'name': function_name,
                                'content': json.dumps(result),
                            },
                        ],
                        response_format={'type': 'json_object'},
                    )

                    return second_response.choices[0].message.content

            # 返回原始响应
            return message.content or '{}'

        except Exception as e:
            logger.error(f'Function calling error: {e}')
            # 出错时返回空JSON
            return '{}'

    @observe(capture_output=False)
    async def evaluate(
        self,
        *,
        llm,
        model: str,
        topic: str,
        title: str | None = None,
        content: str,
        level: Level = Level.GAOZHONG,
        grade: Grade | None = None,
        stream: bool = False,
        tools=None,
    ):
        """支持function calling的评分入口"""

        if level == Level.XIAOXUE and grade is None:
            raise ValueError('小学作文评分必须提供年级信息')

        config = self._configs[level]
        content_length = count_effective_chars(content)
        word_count = config.level.get_word_count(grade)  # 提前计算 word_count

        async def evaluate_step():
            # 1. 题干分析
            analysis_data = await self._analyze_topic(llm, model, topic, title, content)
            if stream:
                yield {'progress': 20, 'step': '正在分析题目要求...'}

            # 2. 确定分值范围
            max_score_limit = (
                config.limit_score
                if analysis_data.get('matching_level') in ['poor', 'acceptable']
                else config.max_score
            )

            # 3. 扣分检查
            deduction_data = await self._check_deductions(
                llm, model, content, title, content_length, word_count, config.level
            )
            if stream:
                yield {'progress': 50, 'step': '正在检查扣分项...'}

            # 4. 内容评分
            content_data = await self._evaluate_content(
                llm,
                model,
                config,
                topic,
                title,
                content,
                analysis_data,
                max_score_limit,
                content_length,
                word_count,
                grade,
            )
            if stream:
                yield {'progress': 80, 'step': '正在评价作文内容...'}

            # 5. 最终评分 - 使用function calling
            final_data = await self._get_final_score(
                llm,
                model,
                analysis_data,
                content_data,
                deduction_data,
                content_length,
                word_count,
                config.level,
                max_score_limit,
                grade,
                tools=tools,
            )
            yield final_data

        if stream:
            return evaluate_step()
        else:
            final_result: dict = {}
            async for result in evaluate_step():
                final_result = result
            return final_result


class ReactImprover:
    """使用ReAct方式进行作文优化"""

    @staticmethod
    async def analyze_improvement(
        llm: AsyncOpenAI,
        model: str,
        topic: str,
        title: str,
        content: str,
        ref_data: dict,
        temperature: float = 0.3,
    ) -> dict:
        """步骤1: 分析改进点"""
        prompt = await PromptTemplates.improve_analysis_prompt(topic, title, content, ref_data)
        if not prompt or not isinstance(prompt, str):
            raise ValueError('提示词无效')

        params = {
            'model': model,
            'temperature': temperature,
            'messages': [
                ChatCompletionSystemMessageParam(content=prompt, role='system'),
            ],
            'response_format': {'type': 'json_object'},
        }
        res = await llm_chat(llm, params)
        data = extract_json(res)
        return data

    async def _generate_improved_content(
        self, llm, model, topic, title, content, ref_data, analysis_data, content_length, word_count, level_score
    ):
        """步骤2: 生成优化内容"""
        improve_content_prompt = await PromptTemplates.improve_content_prompt(
            topic, title, content, ref_data, analysis_data, content_length, word_count, level_score
        )
        # 这里我们不使用extract_json，因为返回的是纯文本内容
        improved_content = await ReactEvaluator._llm_request(llm, model, improve_content_prompt, temperature=0.7)
        return improved_content

    async def _estimate_improved_score(
        self, llm, model, topic, title, original_content, improved_content, ref_data, analysis_data, level_score
    ):
        """步骤3: 预估优化后的分数"""
        improve_score_prompt = await PromptTemplates.improve_score_prompt(
            topic, title, original_content, improved_content, ref_data, analysis_data, level_score
        )
        score_result = await ReactEvaluator._llm_request(llm, model, improve_score_prompt, temperature=0.4)
        return extract_json(score_result)

    async def _generate_improvement_summary(self, llm, model, original_content, improved_content, title, analysis_data):
        """步骤4: 生成优化总结"""
        improve_summary_prompt = await PromptTemplates.improve_summary_prompt(
            original_content, improved_content, title, analysis_data
        )
        summary_result = await ReactEvaluator._llm_request(llm, model, improve_summary_prompt, temperature=0.4)
        # 这里改成返回纯文本，因为改了提示词输出格式
        return summary_result

    @observe(capture_output=False)
    async def improve(
        self,
        *,
        llm: AsyncOpenAI,
        model: str,
        topic: str,
        title: str | None = None,
        content: str,
        ref_data: dict,
        level: Level = Level.GAOZHONG,
        grade: Grade | None = None,
        stream: bool = False,
        **kwargs,
    ) -> Dict[str, Any] | AsyncGenerator[Dict[str, Any], None]:
        """统一的作文优化入口"""
        langfuse_context.update_current_trace(session_id=kwargs.get('uid', None))

        if level == Level.XIAOXUE and grade is None:
            raise ValueError('小学作文优化必须提供年级信息')

        # 获取字数要求
        word_count = level.get_word_count(grade)
        content_length = len(content)

        # 获取满分值
        level_scores = {
            Level.XIAOXUE: 30,
            Level.CHUZHONG: 60,
            Level.GAOZHONG: 60,
        }
        level_score = level_scores[level]

        async def improve_step():
            # 1. 分析改进点
            analysis_data = await self.analyze_improvement(llm, model, topic, title or '', content, ref_data)
            if stream:
                yield {'progress': 25, 'step': '正在分析作文不足点...'}

            # 2. 生成优化内容
            improved_content = await self._generate_improved_content(
                llm,
                model,
                topic,
                title or '',
                content,
                ref_data,
                analysis_data,
                content_length,
                word_count,
                level_score,
            )

            if stream:
                yield {'progress': 50, 'step': '正在生成优化内容...'}

            # 3. 预估优化后的分数
            score_data = await self._estimate_improved_score(
                llm, model, topic, title or '', content, improved_content, ref_data, analysis_data, level_score
            )
            total_score = score_data.get('total_score', 0)

            if stream:
                yield {'progress': 75, 'step': '正在生成优化总结...'}

            # 4. 生成优化总结
            summary = await self._generate_improvement_summary(
                llm, model, content, improved_content, title or '', analysis_data
            )

            # 合并结果
            result = {'total_score': total_score, 'content': improved_content, 'summary': summary}

            if stream:
                yield {'progress': 100, 'step': '优化完成'}

            yield result

        if stream:
            return improve_step()
        else:
            async for result in improve_step():
                if 'progress' not in result:
                    final_result = result
            return final_result  # type: ignore

    @staticmethod
    async def _llm_request(llm, model, prompt, temperature):
        """统一的LLM请求处理"""
        return await llm_chat(
            llm,
            {
                'model': model,
                'temperature': temperature,
                'messages': [
                    ChatCompletionSystemMessageParam(content=prompt, role='system'),
                ],
                'response_format': {'type': 'json_object'},
            },
        )

    @staticmethod
    async def improve_with_length_check(
        llm,
        model,
        system_prompt,
        temperature,
        min_length,
        max_length,
        max_attempts=3,  # 最大尝试次数
    ) -> str:
        # 初始化对话历史（包含系统提示）
        messages = [{'role': 'system', 'content': system_prompt}]
        attempts = 0
        content = ''

        while attempts < max_attempts:
            # 1. 调用模型生成优化内容
            response = await llm.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
            )
            content = response.choices[0].message.content
            terse_content = remove_blank_characters(content)
            current_length = len(terse_content)

            await logger.ainfo('当前轮次和字数', attempts=attempts, current_length=current_length)

            # 2. 检查字数是否合规（字数达到下限即可）
            if min_length <= current_length:
                return content  # 符合要求，直接返回

            # 3. 不符合要求时，构建修正指令
            feedback = PromptTemplates.wordcount_feedback_prompt(current_length, min_length, max_length)

            # 4. 将当前内容和修正指令追加到对话历史
            messages.extend(
                [
                    {'role': 'assistant', 'content': content},  # 上次生成的优化内容
                    {'role': 'user', 'content': feedback},  # 修正要求
                ]
            )
            attempts += 1

        return content
