import json
import math
from typing import AsyncGenerator, TypedDict

from langchain_core.runnables import RunnableConfig
from langgraph.constants import END, START
from langgraph.graph import StateGraph
from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionSystemMessageParam

from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.helper import extract_json

from ..constants import Grade, Level
from ..dto import Progress, ReviewOut
from ..prompts import PromptTemplates
from .tools import ScoreCalculator


class InputState(TypedDict):
    uid: str
    topic: str
    title: str
    content: str
    content_length: int
    level: Level
    grade: Grade | None

    llm_config: llms.LLMConfig
    llm: AsyncOpenAI


class OverallState(InputState):
    analyze_result: dict | None
    max_score_limit: int | None
    deduction_result: dict | None
    calc_result: dict | None
    eval_result: dict | None
    out: ReviewOut | None


class ReviewWorkflow:
    @staticmethod
    async def analyze_topic(state: OverallState):
        llm_config = state['llm_config']

        prompt = await PromptTemplates.topic_analysis_prompt(state['topic'], state['title'], state['content'])
        params = llms.get_params(
            llm_config,
            [ChatCompletionSystemMessageParam(content=prompt, role='system')],
        )

        analysis_result = await llms.llm_chat(state['llm'], params)

        return {'analyze_result': extract_json(analysis_result)}

    @staticmethod
    def get_max_score_limit(state: OverallState):
        matching_level = state['analyze_result'].get('matching_level')  # pyright: ignore[reportOptionalMemberAccess]
        level = state['level']

        if matching_level in ['poor', 'acceptable']:
            max_score_limit = level.get_limit_score()
        else:
            max_score_limit = level.get_score()

        return {'max_score_limit': max_score_limit}

    @staticmethod
    async def check_deductions(state: OverallState):
        llm_config = state['llm_config']
        content_length = state['content_length']
        level = state['level']
        word_count = level.get_word_count(state['grade'])

        prompt = await PromptTemplates.deduction_prompt(
            state['content'],
            state['title'],
            content_length,
            level,
        )
        params = llms.get_params(
            llm_config,
            [ChatCompletionSystemMessageParam(content=prompt, role='system')],
        )

        raw = await llms.llm_chat(state['llm'], params)

        data = extract_json(raw)

        # 作文字数大于正常字数或者小于一半时, 不扣字数分
        if content_length > word_count.normal or content_length < word_count.approximate:
            return {'deduction_result': data}

        cl_diff = math.ceil((word_count.normal - content_length) / 50)
        if level == Level.XIAOXUE:
            score = cl_diff * 1
        else:
            score = cl_diff * 3

        if cl_diff > 0:
            data['word_count_deduction'] = score
            data['total_deduction'] = data['typo_deduction'] + data['punctuation_deduction'] + score

        return {'deduction_result': data}

    @staticmethod
    async def evaluate(state: OverallState):
        llm_config = state['llm_config']
        level = state['level']
        grade = state['grade']
        word_count = level.get_word_count(grade)

        prompt = await PromptTemplates.eval_prompt(
            state['topic'],
            state['title'],
            state['content'],
            state['content_length'],
            state['analyze_result'],  # pyright: ignore[reportArgumentType]
            state['max_score_limit'],  # pyright: ignore[reportArgumentType]
            word_count,
            level,
            grade,
        )

        params = llms.get_params(
            llm_config,
            [ChatCompletionSystemMessageParam(content=prompt, role='system')],
        )

        raw = await llms.llm_chat(state['llm'], params)

        result = extract_json(raw)

        return {'eval_result': result}

    @staticmethod
    def calc_score(state: OverallState):
        eval_result = state['eval_result']

        match state['level']:
            case Level.XIAOXUE:
                calc_func = ScoreCalculator.calculate_xiaoxue_score
            case Level.CHUZHONG:
                calc_func = ScoreCalculator.calculate_chuzhong_score
            case Level.GAOZHONG:
                calc_func = ScoreCalculator.calculate_gaozhong_score

        calc_result = calc_func(
            content_data=json.dumps(eval_result['detail']),  # pyright: ignore[reportOptionalSubscript]
            deduction_data=json.dumps(state['deduction_result']),
        )

        return {'calc_result': calc_result}

    @staticmethod
    async def final_result(state: OverallState):
        """汇总评分数据与计算扣分后的数据"""
        eval_result = state['eval_result']
        calc_result = state['calc_result']

        if level := calc_result.get('level'):  # pyright: ignore[reportOptionalMemberAccess]
            eval_result['level'] = level  # pyright: ignore[reportOptionalSubscript]

        if detail := calc_result.get('detail'):  # pyright: ignore[reportOptionalMemberAccess]
            eval_result['detail'] = detail  # pyright: ignore[reportOptionalSubscript]

        return {'out': eval_result}

    @property
    def workflow(self):
        """
        构建并配置文章评审的 langgraph 工作流图。
        定义了工作流中的节点（步骤）及其之间的转换。
        """
        graph = (
            StateGraph(OverallState, input_schema=InputState, output_schema=OverallState)
            # 定义工作流中的各个节点（异步方法）
            .add_node('analyze_topic', self.analyze_topic)
            .add_node('get_max_score_limit', self.get_max_score_limit)
            .add_node('check_deductions', self.check_deductions)
            .add_node('evaluate', self.evaluate)
            .add_node('calc_score', self.calc_score)
            .add_node('final_result', self.final_result)
            # 定义节点之间的顺序转换
            .add_edge(START, 'analyze_topic')
            .add_edge('analyze_topic', 'get_max_score_limit')
            .add_edge('get_max_score_limit', 'evaluate')
            .add_edge(START, 'check_deductions')
            .add_edge(['check_deductions', 'evaluate'], 'calc_score')
            .add_edge('calc_score', 'final_result')
            .add_edge('final_result', END)
        )

        return graph.compile(name='article_review_workflow')

    async def execute(self, state: InputState) -> AsyncGenerator[Progress | ReviewOut, None]:
        langfuse_handler = llms.get_langchain_handler()
        config = RunnableConfig(callbacks=[langfuse_handler])

        async for chunk in self.workflow.astream(state, config):
            for key, value in chunk.items():
                if key == '__end__':  # 过滤掉 langgraph 内部的结束标记
                    continue

                # 根据当前步骤返回不同的进度信息或最终结果
                if key == 'analyze_topic':
                    yield Progress(progress=20, step='主题分析完成，开始检查错别字...')
                elif key == 'get_max_score_limit':
                    yield Progress(progress=40, step='正在评价作文内容...')
                elif key == 'evaluate':
                    yield Progress(progress=60, step='正在计算得分...')
                elif key == 'calc_score':
                    yield Progress(progress=80, step='正在生成最终结果...')
                elif key == 'final_result':
                    yield Progress(progress=100, step='最终结果生成完成')
                    yield value['out']

    def _get_mermaid_png(self):
        # 获取 PNG 数据
        png_data = self.workflow.get_graph().draw_mermaid_png()

        # 定义文件名
        output_filename = 'workflow_graph.png'

        # 将 PNG 数据保存到文件
        with open(output_filename, 'wb') as f:
            f.write(png_data)
