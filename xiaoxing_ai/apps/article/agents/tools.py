"""
作文评分计算工具模块
用于处理作文评分计算逻辑，支持OpenAI function calling
"""

import json
from typing import Any, Dict, Optional

from langfuse.decorators import langfuse_context, observe

from ..constants import Level


class ScoreCalculator:
    """作文评分计算器"""

    @staticmethod
    def round_to_half(value: float) -> float:
        """将值四舍五入到最近的0.5倍数"""
        return round(value * 2) / 2

    @staticmethod
    def calc_ratio(value: float, divisor: float) -> float:
        """计算百分比"""
        if divisor == 0:
            return 0
        return value / divisor

    @classmethod
    def calc_deduction(cls, a: float | int, b: float) -> float:
        """计算扣分"""
        s = a * b
        return cls.round_to_half(s)

    @classmethod
    @observe(capture_output=False)
    def calculate_gaozhong_score(cls, content_data: str, deduction_data: str) -> Dict[str, Any]:
        """高中作文评分计算函数"""
        # 解析JSON字符串
        content_dict = json.loads(content_data)
        deduction_dict = json.loads(deduction_data)

        # 提取分数
        neirong_score = content_dict['neirong']['score']
        biaoda_score = content_dict['biaoda']['score']
        tezheng_score = content_dict['tezheng']['score']
        total_deduction = deduction_dict['total_deduction']  # 扣分分值

        shenti_score = content_dict['neirong']['detail']['shenti']['score']
        liyi_score = content_dict['neirong']['detail']['liyi']['score']
        wenti_score = content_dict['biaoda']['detail']['wenti']['score']
        jiegou_score = content_dict['biaoda']['detail']['jiegou']['score']
        shenke_score = content_dict['tezheng']['detail']['shenke']['score']
        chuangxin_score = content_dict['tezheng']['detail']['chuangxin']['score']

        # 扣分比例计算规则：
        # 细项百分比 =  细项/总分
        # 将需要扣除的分数按比例分配到各细分项
        # 细项扣除分数 = （细项 * 细项百分比） /0.5 变为0.5的倍数
        # 最总细项分数 = 细项 - 细项扣除分数

        other_total = shenti_score + liyi_score + wenti_score + jiegou_score + shenke_score + chuangxin_score

        # 计算扣分占比
        wenti_ratio = cls.calc_ratio(wenti_score, other_total)
        jiegou_ratio = cls.calc_ratio(jiegou_score, other_total)
        shenti_ratio = cls.calc_ratio(shenti_score, other_total)
        liyi_ratio = cls.calc_ratio(liyi_score, other_total)
        shenke_ratio = cls.calc_ratio(shenke_score, other_total)
        # chuangxin_ratio = cls.calc_ratio(chuangxin_score, other_total)

        # 计算各部分扣分 应该为0.5的倍数
        wenti_deduction = cls.calc_deduction(total_deduction, wenti_ratio)
        jiegou_deduction = cls.calc_deduction(total_deduction, jiegou_ratio)
        shenti_deduction = cls.calc_deduction(total_deduction, shenti_ratio)
        liyi_deduction = cls.calc_deduction(total_deduction, liyi_ratio)
        shenke_deduction = cls.calc_deduction(total_deduction, shenke_ratio)
        # chuangxin_deduction = cls.calc_deduction(total_deduction, chuangxin_ratio)
        # 最后一项由总分减去其他项的和
        chuangxin_deduction = total_deduction - (
            wenti_deduction + jiegou_deduction + shenti_deduction + liyi_deduction + shenke_deduction
        )

        # 更新各细分项最终得分
        final_wenti_score = wenti_score - wenti_deduction
        final_jiegou_score = jiegou_score - jiegou_deduction
        final_shenti_score = shenti_score - shenti_deduction
        final_liyi_score = liyi_score - liyi_deduction
        final_shenke_score = shenke_score - shenke_deduction
        final_chuangxin_score = chuangxin_score - chuangxin_deduction

        # 更新各板块总分
        final_neirong_score = final_shenti_score + final_liyi_score
        final_biaoda_score = final_wenti_score + final_jiegou_score
        final_tezheng_score = final_shenke_score + final_chuangxin_score

        # 计算最终总分
        total_score = final_neirong_score + final_biaoda_score + final_tezheng_score

        # 确定等级
        if total_score >= 53:
            level = '一等'
        elif total_score >= 43:
            level = '二等'
        elif total_score >= 30:
            level = '三等'
        else:
            level = '四等'

        # 计算过程
        calculation_process = [
            '计算过程:',
            f'原始分数 - 内容:{neirong_score} 表达:{biaoda_score} 特征:{tezheng_score}',
            f'总扣分:{total_deduction}',
            f'最终分数 - 内容:{final_neirong_score} 表达:{final_biaoda_score} 特征:{final_tezheng_score}',
            f'最终总分:{total_score}',
            f'作文等级:{level}',
        ]

        # 更新详细数据
        updated_detail = {
            'neirong': {
                **content_dict['neirong'],
                'score': final_neirong_score,
                'detail': {
                    'shenti': {**content_dict['neirong']['detail']['shenti'], 'score': final_shenti_score},
                    'liyi': {**content_dict['neirong']['detail']['liyi'], 'score': final_liyi_score},
                },
            },
            'biaoda': {
                **content_dict['biaoda'],
                'score': final_biaoda_score,
                'detail': {
                    'wenti': {**content_dict['biaoda']['detail']['wenti'], 'score': final_wenti_score},
                    'jiegou': {**content_dict['biaoda']['detail']['jiegou'], 'score': final_jiegou_score},
                },
            },
            'tezheng': {
                **content_dict['tezheng'],
                'score': final_tezheng_score,
                'detail': {
                    'shenke': {**content_dict['tezheng']['detail']['shenke'], 'score': final_shenke_score},
                    'chuangxin': {**content_dict['tezheng']['detail']['chuangxin'], 'score': final_chuangxin_score},
                },
            },
        }

        data = {
            'total_score': total_score,
            'level': level,
            'detail': updated_detail,
            'calculation_process': '\n'.join(calculation_process),
        }

        langfuse_context.update_current_observation(output=data)

        return data

    @classmethod
    @observe(capture_output=False)
    def calculate_chuzhong_score(cls, content_data: str, deduction_data: str) -> Dict[str, Any]:
        """初中作文评分计算函数"""
        # 解析JSON字符串
        content_dict = json.loads(content_data)
        deduction_dict = json.loads(deduction_data)

        # 提取分数
        liyi_score = content_dict['liyi']['score']
        neirong_score = content_dict['neirong']['score']
        chuangyi_score = content_dict['chuangyi']['score']
        yuyan_score = content_dict['yuyan']['score']
        jiegou_score = content_dict['jiegou']['score']

        total_deduction = deduction_dict['total_deduction']

        # 应用扣分
        other_total = yuyan_score + liyi_score + neirong_score + chuangyi_score + jiegou_score

        # 计算扣分占比
        yuyan_ratio = cls.calc_ratio(yuyan_score, other_total)
        liyi_ratio = cls.calc_ratio(liyi_score, other_total)
        neirong_ratio = cls.calc_ratio(neirong_score, other_total)
        chuangyi_ratio = cls.calc_ratio(chuangyi_score, other_total)
        # jiegou_ratio = cls.calc_ratio(jiegou_score, other_total)

        # 计算各部分扣分
        yuyan_deduction = cls.calc_deduction(total_deduction, yuyan_ratio)
        liyi_deduction = cls.calc_deduction(total_deduction, liyi_ratio)
        neirong_deduction = cls.calc_deduction(total_deduction, neirong_ratio)
        chuangyi_deduction = cls.calc_deduction(total_deduction, chuangyi_ratio)
        # jiegou_deduction = cls.calc_deduction(total_deduction, jiegou_ratio)

        jiegou_deduction = total_deduction - (yuyan_deduction + liyi_deduction + neirong_deduction + chuangyi_deduction)

        # 更新各板块最终得分
        final_yuyan_score = yuyan_score - yuyan_deduction
        final_liyi_score = liyi_score - liyi_deduction
        final_neirong_score = neirong_score - neirong_deduction
        final_chuangyi_score = chuangyi_score - chuangyi_deduction
        final_jiegou_score = jiegou_score - jiegou_deduction

        # 计算最终总分
        total_score = (
            final_liyi_score + final_neirong_score + final_chuangyi_score + final_yuyan_score + final_jiegou_score
        )

        # 确定等级
        if total_score >= 53:
            level = '一等'
        elif total_score >= 43:
            level = '二等'
        elif total_score >= 33:
            level = '三等'
        elif total_score >= 24:
            level = '四等'
        else:
            level = '五等'

        # 计算过程
        calculation_process = [
            '计算过程:',
            f'原始分数 - 立意:{liyi_score} 内容:{neirong_score} 创意:{chuangyi_score} 语言:{yuyan_score} 结构:{jiegou_score}',  # noqa: E501
            f'总扣分:{total_deduction}',
            f'最终分数 - 立意:{final_liyi_score} 内容:{final_neirong_score} 创意:{final_chuangyi_score} 语言:{final_yuyan_score} 结构:{final_jiegou_score}',  # noqa: E501
            f'最终总分:{total_score}',
            f'作文等级:{level}',
        ]

        # 更新详细数据
        updated_detail = {
            'liyi': {**content_dict['liyi'], 'score': final_liyi_score},
            'neirong': {**content_dict['neirong'], 'score': final_neirong_score},
            'chuangyi': {**content_dict['chuangyi'], 'score': final_chuangyi_score},
            'yuyan': {**content_dict['yuyan'], 'score': final_yuyan_score},
            'jiegou': {**content_dict['jiegou'], 'score': final_jiegou_score},
        }

        data = {
            'total_score': total_score,
            'level': level,
            'detail': updated_detail,
            'calculation_process': '\n'.join(calculation_process),
        }
        langfuse_context.update_current_observation(output=data)

        return data

    @classmethod
    @observe(capture_output=False)
    def calculate_xiaoxue_score(cls, content_data: str, deduction_data: str) -> Dict[str, Any]:
        """小学作文评分计算函数"""
        # 解析JSON字符串
        content_dict = json.loads(content_data)
        deduction_dict = json.loads(deduction_data)

        # 提取分数
        zhongxin_score = content_dict['zhongxin']['score']
        biaoda_score = content_dict['biaoda']['score']
        neirong_score = content_dict['neirong']['score']
        duanluo_score = content_dict['duanluo']['score']
        total_deduction = deduction_dict['total_deduction']

        # 应用扣分
        other_total = biaoda_score + zhongxin_score + neirong_score + duanluo_score

        # 计算扣分占比
        biaoda_ratio = cls.calc_ratio(biaoda_score, other_total)
        zhongxin_ratio = cls.calc_ratio(zhongxin_score, other_total)
        neirong_ratio = cls.calc_ratio(neirong_score, other_total)
        # duanluo_ratio = cls.calc_ratio(duanluo_score, other_total)

        # 计算各部分扣分
        biaoda_deduction = cls.calc_deduction(total_deduction, biaoda_ratio)
        zhongxin_deduction = cls.calc_deduction(total_deduction, zhongxin_ratio)
        neirong_deduction = cls.calc_deduction(total_deduction, neirong_ratio)
        # duanluo_deduction = cls.calc_deduction(total_deduction, duanluo_ratio)
        duanluo_deduction = total_deduction - (biaoda_deduction + zhongxin_deduction + neirong_deduction)

        # 更新各板块最终得分
        final_biaoda_score = biaoda_score - biaoda_deduction
        final_zhongxin_score = zhongxin_score - zhongxin_deduction
        final_neirong_score = neirong_score - neirong_deduction
        final_duanluo_score = duanluo_score - duanluo_deduction

        # 计算最终总分
        total_score = final_zhongxin_score + final_biaoda_score + final_neirong_score + final_duanluo_score

        # 确定等级
        if total_score >= 27:
            level = '一等'
        elif total_score >= 23:
            level = '二等'
        elif total_score >= 19:
            level = '三等'
        elif total_score >= 15:
            level = '四等'
        else:
            level = '五等'

        # 计算过程
        calculation_process = [
            '计算过程:',
            f'原始分数 - 中心思想:{zhongxin_score} 表达:{biaoda_score} 内容:{neirong_score} 段落:{duanluo_score}',
            f'总扣分:{total_deduction}',
            f'最终分数 - 中心思想:{final_zhongxin_score} 表达:{final_biaoda_score} 内容:{final_neirong_score} 段落:{final_duanluo_score}',  # noqa: E501
            f'最终总分:{total_score}',
            f'作文等级:{level}',
        ]

        # 更新详细数据
        updated_detail = {
            'zhongxin': {**content_dict['zhongxin'], 'score': final_zhongxin_score},
            'biaoda': {**content_dict['biaoda'], 'score': final_biaoda_score},
            'neirong': {**content_dict['neirong'], 'score': final_neirong_score},
            'duanluo': {**content_dict['duanluo'], 'score': final_duanluo_score},
        }

        data = {
            'total_score': total_score,
            'level': level,
            'detail': updated_detail,
            'calculation_process': '\n'.join(calculation_process),
        }

        langfuse_context.update_current_observation(output=data)

        return data

    @staticmethod
    def calculate_final_score(
        content_data: str, deduction_data: str, level_grade: str, max_score_limit: Optional[int] = None
    ) -> Dict[str, Any]:
        """根据作文等级选择对应的计算函数计算最终得分"""
        try:
            if level_grade == Level.GAOZHONG.value:
                result = ScoreCalculator.calculate_gaozhong_score(content_data, deduction_data)
            elif level_grade == Level.CHUZHONG.value:
                result = ScoreCalculator.calculate_chuzhong_score(content_data, deduction_data)
            elif level_grade == Level.XIAOXUE.value:
                result = ScoreCalculator.calculate_xiaoxue_score(content_data, deduction_data)
            else:
                raise ValueError(f'不支持的作文等级: {level_grade}')

            # 限制最高分
            if max_score_limit is not None:
                result['total_score'] = min(result['total_score'], max_score_limit)

            return result
        except Exception as e:
            # 解析JSON字符串以获取原始content_data用于错误返回
            try:
                content_dict = json.loads(content_data)
            except:  # noqa: E722
                content_dict = {}

            # 发生错误时返回默认结果
            return {
                'error': str(e),
                'total_score': 0,
                'level': '无法评定',
                'detail': content_dict,
                'calculation_process': f'计算过程出错: {str(e)}',
            }


# 提供给OpenAI函数调用的API schema
FUNCTION_SCHEMA = {
    'name': 'calculate_article_score',
    'description': '根据作文评分数据计算最终分数',
    'parameters': {
        'type': 'object',
        'properties': {
            'level_grade': {
                'type': 'string',
                'description': '作文等级: 小学/初中/高中',
                'enum': ['小学', '初中', '高中'],
            },
            'content_data': {'type': 'string', 'description': '内容评分数据'},
            'deduction_data': {'type': 'string', 'description': '扣分项数据'},
            'max_score_limit': {'type': 'integer', 'description': '最高分值上限'},
        },
        'required': [
            'level_grade',
            'content_data',
            'deduction_data',
            'max_score_limit',
        ],
    },
}
