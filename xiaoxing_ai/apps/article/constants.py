from dataclasses import dataclass
from enum import IntEnum, StrEnum

from pydantic import BaseModel, Field


class Grade(IntEnum):
    THREE = 3
    FOUR = 4
    FIVE = 5
    SIX = 6


@dataclass
class WordCount:
    max: int  # 最多字数
    normal: int  # 正常字数
    similar: int  # 相似字数, 评价时需要扣分的字数
    approximate: int  # 近似字数, 实际为一半字数 half
    more: int  # 较多字数
    less: int  # 较少字数


class Level(StrEnum):
    XIAOXUE = '小学'
    CHUZHONG = '初中'
    GAOZHONG = '高中'

    def get_word_count(self, grade: Grade | None) -> WordCount:
        """字数映射"""
        mapping = {
            self.XIAOXUE: {
                Grade.THREE: WordCount(700, 300, 200, 150, 75, 30),
                Grade.FOUR: WordCount(800, 400, 300, 200, 100, 30),
                Grade.FIVE: WordCount(900, 500, 400, 250, 125, 30),
                Grade.SIX: WordCount(1000, 600, 400, 300, 150, 30),
            },
            self.CHUZHONG: WordCount(1000, 600, 400, 300, 150, 40),
            self.GAOZHONG: WordCount(1200, 800, 600, 400, 200, 40),
        }

        res = mapping[self]
        if isinstance(res, WordCount):
            return res

        if grade is None:
            # 最终兜底
            grade = Grade.THREE

        return res[grade]

    def get_score(self):
        mapping = {
            self.XIAOXUE: 30,
            self.CHUZHONG: 60,
            self.GAOZHONG: 60,
        }

        return mapping[self]

    def get_limit_score(self):
        mapping = {
            self.XIAOXUE: 18,
            self.CHUZHONG: 32,
            self.GAOZHONG: 30,
        }

        return mapping[self]


class DeductionRules(BaseModel):
    """
    作文扣分项的具体数值标准。
    """

    word_deduction_per_point: float = Field(..., description='错别字每字扣分')
    word_deduction_max_point: float = Field(..., description='错别字扣分上限')
    punctuation_deduction_min_count: int = Field(..., description='标点符号错误最少出现次数')
    punctuation_deduction_max_point: float = Field(..., description='标点符号扣分上限')
    title_deduction_point: float = Field(..., description='标题扣分')


# 定义一个存储所有学段规则的字典
ALL_DEDUCTION_STANDARDS: dict[str, DeductionRules] = {
    Level.GAOZHONG: DeductionRules(
        word_deduction_per_point=1,
        word_deduction_max_point=5,
        punctuation_deduction_min_count=3,
        punctuation_deduction_max_point=2,
        title_deduction_point=2,
    ),
    Level.CHUZHONG: DeductionRules(
        word_deduction_per_point=1,
        word_deduction_max_point=4,
        punctuation_deduction_min_count=3,
        punctuation_deduction_max_point=2,
        title_deduction_point=2,
    ),
    Level.XIAOXUE: DeductionRules(
        word_deduction_per_point=0.5,
        word_deduction_max_point=3,
        punctuation_deduction_min_count=3,
        punctuation_deduction_max_point=1,
        title_deduction_point=1,
    ),
}


class ImprovedStatus(IntEnum):
    PENDING = 0
    SUCCESS = 1
    FAILED = 2
    TIMEOUT = 3


REVIEW_CACHE_KEY = 'xiaoxing_ai:article:{date}:{md5_key}'
