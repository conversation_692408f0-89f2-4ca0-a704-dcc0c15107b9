import bisect
from typing import Literal, overload

from diff_match_patch import diff_match_patch

from .dto import DiffItem, DiffOut

dmp = diff_match_patch()
dmp.Diff_Timeout = 3  # 0 表示无超时


def compute_line_starts(text: str) -> list[int]:
    """计算文本中每行的起始绝对字符位置（索引）"""
    return [0] + [i + 1 for i, c in enumerate(text) if c == '\n']


def get_line_info(abs_index: int, line_starts: list[int]) -> tuple[int, int]:
    """
    根据绝对字符索引，返回对应的行号（1-based）和行内字符偏移
    """
    line_num = bisect.bisect_right(line_starts, abs_index)
    line_start = line_starts[line_num - 1] if line_num > 0 else 0
    return line_num, abs_index - line_start


@overload
def get_diff(old_text: str, new_text: str, is_html: Literal[True]) -> str: ...
@overload
def get_diff(old_text: str, new_text: str, is_html: Literal[False] = False) -> DiffOut: ...
def get_diff(old_text: str, new_text: str, is_html: bool = False) -> str | DiffOut:
    """
    计算两个文本的差异，返回一个列表，
    每项描述了插入、删除或替换操作，并标明对应的文本位置。
    """

    diffs = dmp.diff_main(old_text, new_text)
    dmp.diff_cleanupMerge(diffs)
    dmp.diff_cleanupEfficiency(diffs)

    if is_html:
        return dmp.diff_prettyHtml(diffs)

    old_line_starts = compute_line_starts(old_text)
    new_line_starts = compute_line_starts(new_text)

    current_old_idx = 0  # 旧文本当前绝对字符索引
    current_new_idx = 0  # 新文本当前绝对字符索引

    results = {'insert': [], 'delete': [], 'update': []}
    skip_next = False  # 是否跳过下一条（处理替换时用）

    for i in range(len(diffs)):
        if skip_next:
            skip_next = False
            continue

        op, text = diffs[i]
        length = len(text)

        old_line, old_char = get_line_info(current_old_idx, old_line_starts)
        new_line, new_char = get_line_info(current_new_idx, new_line_starts)

        if op == -1 and i + 1 < len(diffs) and diffs[i + 1][0] == 1:
            # 发现替换（删除 + 插入）
            deleted_text = text
            inserted_text = diffs[i + 1][1]

            results['update'].append(
                [
                    DiffItem(key='old', content=deleted_text, index=f'{old_line}:{old_char}'),
                    DiffItem(key='new', content=inserted_text, index=f'{new_line}:{new_char}'),
                ]
            )
            current_old_idx += len(deleted_text)
            current_new_idx += len(inserted_text)
            skip_next = True  # 跳过下一个插入，因为已处理
        elif op == -1:
            # 删除
            results['delete'].append(DiffItem(key='old', content=text, index=f'{old_line}:{old_char}'))
            current_old_idx += length
        elif op == 1:
            # 插入
            results['insert'].append(DiffItem(key='new', content=text, index=f'{new_line}:{new_char}'))
            current_new_idx += length
        else:
            # 相同内容
            current_old_idx += length
            current_new_idx += length

    return DiffOut(**results)
