from typing import Self

from pydantic import BaseModel, Field, model_validator

from xiaoxing_ai.shared.helper import check_scores_in_range


class ItemBase(BaseModel):
    label: str = Field(..., description='名称')
    score: int | float = Field(..., description='分数')


class DetailItem(ItemBase):
    summary: str = Field(..., description='评价')


class XiaoxueReviewSchema(BaseModel):
    """小学结构"""

    zhongxin: DetailItem = Field(..., description='中心思想')
    biaoda: DetailItem = Field(..., description='表达')
    neirong: DetailItem = Field(..., description='内容')
    duanluo: DetailItem = Field(..., description='段落')

    @model_validator(mode='after')
    def check_scores(self) -> Self:
        score_limit = {
            'zhongxin': (0, 9),
            'biaoda': (0, 6),
            'neirong': (0, 9),
            'duanluo': (0, 6),
        }
        return check_scores_in_range(self, score_limit)  # type: ignore


class ChuzhongReviewSchema(BaseModel):
    """初中结构"""

    liyi: DetailItem = Field(..., description='立意')
    neirong: DetailItem = Field(..., description='内容')
    chuangyi: DetailItem = Field(..., description='创意')
    yuyan: DetailItem = Field(..., description='语言')
    jiegou: DetailItem = Field(..., description='结构')

    @model_validator(mode='after')
    def check_scores_in_range_cz(self) -> Self:
        score_limit = {
            'liyi': (0, 10),
            'neirong': (0, 15),
            'chuangyi': (0, 10),
            'yuyan': (0, 15),
            'jiegou': (0, 10),
        }
        return check_scores_in_range(self, score_limit)  # type: ignore


class neirongItem(BaseModel):
    shenti: ItemBase = Field(..., description='审题')
    liyi: ItemBase = Field(..., description='立意')


class biaodaItem(BaseModel):
    wenti: ItemBase = Field(..., description='文体')
    jiegou: ItemBase = Field(..., description='结构')


class tezhengItem(BaseModel):
    shenke: ItemBase = Field(..., description='深刻')
    chuangxin: ItemBase = Field(..., description='创新')


class GaozhongDetailItem(DetailItem):
    level: str = Field(..., description='等级', examples=['一等', '二等'])


class NeiRongItem(GaozhongDetailItem):
    detail: neirongItem = Field(..., description='更详细的字段')


class BiaoDaItem(GaozhongDetailItem):
    detail: biaodaItem = Field(..., description='更详细的字段')


class TeZhengItem(GaozhongDetailItem):
    detail: tezhengItem = Field(..., description='更详细的字段')


class GaozhongReviewSchema(BaseModel):
    """高中结构"""

    neirong: NeiRongItem = Field(..., description='内容')
    biaoda: BiaoDaItem = Field(..., description='表达')
    tezheng: TeZhengItem = Field(..., description='特征')

    @model_validator(mode='after')
    def check_scores_in_range(self) -> Self:
        """
        在模型数据被初始化时，校验每个评分项的分数是否在合法范围内，
        如果超出范围，则调整为最小或最大值。
        同时，检查外围评分项的总分是否等于内部评分项的总和，
        如果不等，则调整为内部分数的和。
        """

        score_limit = {
            'neirong': {'shenti': (0, 10), 'liyi': (0, 10)},
            'biaoda': {'wenti': (0, 10), 'jiegou': (0, 10)},
            'tezheng': {'shenke': (0, 10), 'chuangxin': (0, 10)},
        }

        for category, items in score_limit.items():
            cate_item = getattr(self, category, None)
            if cate_item is None:
                continue
            cate_item.detail = check_scores_in_range(cate_item.detail, items)
            cate_item.score = sum([item.score for (_, item) in cate_item.detail])

        for _, item in self:
            item.score = sum([item.score for (_, item) in item.detail])

        return self


class ReviewOut(BaseModel):
    review_id: str = Field(..., description='本轮评测UUID')
    total_score: int | float = Field(..., description='本轮评测总分（由各维度分数相加得到）')
    summary: str = Field(..., description='总体评价')
    level: str = Field(..., description='作文等级')
    detail: XiaoxueReviewSchema | ChuzhongReviewSchema | GaozhongReviewSchema = Field(..., description='评价细项')

    @model_validator(mode='after')
    def check_level(self) -> Self:
        if not self.level.endswith('文'):
            self.level = f'{self.level}文'
        # 计算总分
        score = sum([item.score for (_, item) in self.detail])
        # 默认的数据小项都为0
        if score != 0:
            self.total_score = score

        return self


class ImproveOut(BaseModel):
    content: str = Field(..., description='修改后的文章内容')
    total_score: int | float = Field(..., description='修改后的评分')


class EnhanceImproveOut(BaseModel):
    content: str = Field(..., description='修改后的文章内容')
    total_score: int | float = Field(..., description='修改后的评分')
    summary: str = Field('', description='优化总结，从内容、语言、结构三个方面对比改进')


class Summary(BaseModel):
    summary: str = Field('', description='优化总结，从内容、语言、结构三个方面对比改进')


class Progress(BaseModel):
    progress: int = Field(..., description='进度')
    step: str = Field(..., description='步骤描述')


class ImprovedResultOut(BaseModel):
    status: int = Field(..., description='优化结果状态')
    score: float = Field(..., description='优化后的评分')


class ImprovedProcessResult(BaseModel):
    uid: str = Field(..., description='优化记录UUID')
    improved_content: str = Field(..., description='优化后的文章内容')
    summary: str = Field(..., description='优化总结')
