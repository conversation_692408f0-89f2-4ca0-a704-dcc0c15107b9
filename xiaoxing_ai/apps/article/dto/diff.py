from pydantic import BaseModel, Field


class DiffIn(BaseModel):
    old_text: str = Field(..., description='原始文本')
    new_text: str = Field(..., description='新文本')


class DiffItem(BaseModel):
    key: str = Field(..., description='标记发送位置,old:旧作文; new:新作文')
    content: str = Field(..., description='具体内容')
    index: str = Field(..., description='发送修改的位置,格式:行号:列;如 1:12')


class DiffOut(BaseModel):
    insert: list[DiffItem] = Field(..., description='新插入的文本列表')
    delete: list[DiffItem] = Field(..., description='删除的文本列表')
    update: list[list[DiffItem]] = Field(
        ..., description='更新的文本列表, 返回的是一个嵌套数组,内部数组为一组修改,即将old修改为new'
    )
