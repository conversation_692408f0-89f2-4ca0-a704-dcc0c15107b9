from pydantic import BaseModel, Field

from ..constants import Grade, Level


class Elements(BaseModel):
    course_prompt: str = Field(..., description='课程提示词要求')
    course_object: str = Field(..., description='课程教学目标')
    course_core_tasks: str = Field(..., description='课程核心任务')
    framework: str = Field(..., description='作业基本框架')
    core_tasks: str = Field(..., description='作业核心任务')


class BaseHomework(BaseModel):
    """基础的作业字段"""

    course_title: str = Field(..., description='课程标题')
    course_key_points: str = Field(..., description='课程要点')
    require: str = Field(..., description='课后作业(作业要求)')
    word_count: tuple[int, int] = Field(description='作业字数范围（两个整数，如 [300, 800]）分別代表最小和最大字數）')
    content: str = Field(..., description='作业答案')
    elements: Elements = Field(..., description='指标要素')


class EvaluationIn(BaseHomework):
    level: Level = Field(Level.GAOZHONG, description='作文等级', examples=['小学', '初中', '高中'])
    grade: Grade | None = Field(None, description='小学的年级', examples=['3'])


class EvaluationOut(BaseModel):
    uuid: str = Field(..., description='uuid,记录id')
    level: str = Field(..., description='评价等级; A+;A;B+;B;B-;C+;C;C-;D+;D')
    summary: str = Field(...)


class ImprovedIn(EvaluationIn): ...
