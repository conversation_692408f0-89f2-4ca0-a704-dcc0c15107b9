import uuid

from pydantic import BaseModel, Field

from xiaoxing_ai.consts import ChatEventType


class QAIn(BaseModel):
    """问答输入模型"""

    session_id: uuid.UUID | None = Field(default=None, description='会话ID，如果为空则创建新会话')
    query: str = Field(..., description='用户问题')
    title: str = Field(..., description='课程标题')


class QAMessage(BaseModel):
    """聊天会话"""

    session_id: uuid.UUID | str = Field(..., description='会话ID')
    content: str = Field(..., description='消息内容')
    type: ChatEventType = Field(
        default=ChatEventType.COMPLETION,
        description='需要处理的事件:"completion"-默认拼接,"clear"-清除之前的内容',
    )
