"""
作文评分公共提示词模块
用于存储各种评分阶段的提示词模板
"""

import json

from fastapi import HTTPException
from jinja2 import Template

from xiaoxing_ai.db import AsyncSessionLocal

from .constants import ALL_DEDUCTION_STANDARDS, Grade, Level, WordCount
from .repository import ArticleRepository


class PromptTemplates:
    @staticmethod
    async def get_prompt_template(template_key: str) -> Template:
        """核心方法：获取数据库模板并渲染（其他方法只需调用此方法）"""
        async with AsyncSessionLocal() as db:
            prompt_template = await ArticleRepository(db).get_model_prompt(template_key)
            if not prompt_template:
                raise HTTPException(status_code=404, detail='未找到模板')

            return prompt_template

    @classmethod
    async def _render_template(cls, template_key: str, **kwargs) -> str:
        """通用模板渲染方法，减少重复代码"""
        prompt_template = await cls.get_prompt_template(template_key)
        if not prompt_template:
            return ''
        return prompt_template.render(**kwargs)

    @classmethod
    async def topic_analysis_prompt(cls, topic, title, content) -> str:
        """题干分析与匹配判断提示词"""
        return await cls._render_template('topic_analysis', topic=topic, title=title, content=content)

    # 保留这些方法以维持向后兼容性，但内部使用统一的eval_prompt方法
    @classmethod
    async def gaozhong_content_eval_prompt(
        cls,
        topic,
        content,
        analysis_data,
        title,
        max_score_limit,
        content_length,
        word_count,
        grade,
    ) -> str:
        """高中作文内容评分提示词 - 已弃用，请使用eval_prompt"""
        return await cls.eval_prompt(
            topic=topic,
            title=title,
            content=content,
            content_length=content_length,
            analysis_data=analysis_data,
            max_score_limit=max_score_limit,
            word_count=word_count,
            level=Level.GAOZHONG,
            grade=grade,
        )

    @classmethod
    async def chuzhong_content_eval_prompt(
        cls,
        topic,
        content,
        analysis_data,
        title,
        max_score_limit,
        content_length,
        word_count,
        grade,
    ) -> str:
        """初中作文内容评分提示词 - 已弃用，请使用eval_prompt"""
        return await cls.eval_prompt(
            topic=topic,
            title=title,
            content=content,
            content_length=content_length,
            analysis_data=analysis_data,
            max_score_limit=max_score_limit,
            word_count=word_count,
            level=Level.CHUZHONG,
            grade=grade,
        )

    @classmethod
    async def xiaoxue_content_eval_prompt(
        cls,
        topic,
        content,
        analysis_data,
        title,
        max_score_limit,
        content_length,
        word_count,
        grade,
    ) -> str:
        """小学作文内容评分提示词 - 已弃用，请使用eval_prompt"""
        return await cls.eval_prompt(
            topic=topic,
            title=title,
            content=content,
            content_length=content_length,
            analysis_data=analysis_data,
            max_score_limit=max_score_limit,
            word_count=word_count,
            level=Level.XIAOXUE,
            grade=grade,
        )

    SCORE_MAX_LIMIT_MAPPING = {
        Level.XIAOXUE: 15,
        Level.CHUZHONG: 20,
        Level.GAOZHONG: 20,
    }

    @classmethod
    async def eval_prompt(
        cls,
        topic: str,
        title: str,
        content: str,
        content_length: int,
        analysis_data: dict,
        max_score_limit: int,
        word_count: WordCount,
        level: Level,
        grade: Grade | None,
    ) -> str:
        """作文内容评分提示词"""

        # 当字数小于近似字数要求时，设置最高分上限
        if content_length < word_count.approximate:
            max_score_limit = min(max_score_limit, cls.SCORE_MAX_LIMIT_MAPPING[level])

        prompt_template = await cls.get_prompt_template(f'eval_{level.name.lower()}_v2')
        if not prompt_template:
            return ''

        return prompt_template.render(
            topic=topic,
            title=title,
            content=content,
            analysis_data=analysis_data,
            max_score_limit=max_score_limit,
            content_length=content_length,
            word_count=word_count,
            grade=grade,
        )

    @classmethod
    async def deduction_prompt(cls, content, title, content_length, level) -> str:
        """扣分项检查提示词"""
        eval_deduction = ALL_DEDUCTION_STANDARDS[level]

        if not eval_deduction:
            raise ValueError(f'无效的学段：{level}。请使用 {", ".join(ALL_DEDUCTION_STANDARDS.keys())}')

        prompt_template = await cls.get_prompt_template('eval_deduction')
        if not prompt_template:
            return ''

        return prompt_template.render(
            content=content,
            title=title,
            content_length=content_length,
            eval_deduction=eval_deduction,
        )

    @classmethod
    async def final_score_prompt(
        cls,
        analysis_data,
        content_data,
        deduction_data,
        content_length,
        word_count,
        level,
        max_score_limit,
        grade=None,
    ) -> str:
        """最终评分生成提示词"""
        prompt_template = await cls.get_prompt_template('final_score')
        if not prompt_template:
            return ''

        return prompt_template.render(
            level=level,
            content_length=content_length,
            grade=grade,
            analysis_data=analysis_data,
            word_count=word_count,
            dumps_content_data=json.dumps(content_data, ensure_ascii=False),
            dumps_deduction_data=json.dumps(deduction_data, ensure_ascii=False),
            max_score_limit=max_score_limit,
        )

    @classmethod
    async def improve_analysis_prompt(cls, topic, title, content, ref_data) -> str:
        """作文优化分析提示词"""
        prompt_template = await cls.get_prompt_template('improve_analysis')
        if not prompt_template:
            return ''

        return prompt_template.render(
            topic=topic,
            title=title,
            content=content,
            dumps_ref_data=json.dumps(ref_data, ensure_ascii=False),
        )

    @classmethod
    async def improve_content_prompt(
        cls,
        topic,
        title,
        content,
        ref_data,
        analysis_data,
        content_length,
        word_count,
        level_score,
    ) -> str:
        """作文内容优化提示词"""
        prompt_template = await cls.get_prompt_template('improve_content')
        if not prompt_template:
            return ''

        return prompt_template.render(
            topic=topic,
            title=title,
            content=content,
            content_length=content_length,
            dumps_ref_data=json.dumps(ref_data, ensure_ascii=False),
            dumps_analysis_data=json.dumps(analysis_data, ensure_ascii=False),
            word_count=word_count,
        )

    @classmethod
    async def improve_content_prompt_v2(
        cls,
        topic,
        title,
        content,
        ref_data,
        analysis_data,
        content_length,
        word_count,
        level_score,
    ) -> str:
        """作文内容优化提示词"""
        prompt_template = await cls.get_prompt_template('improve_content_v2')
        if not prompt_template:
            return ''

        return prompt_template.render(
            topic=topic,
            title=title,
            content=content,
            content_length=content_length,
            dumps_ref_data=json.dumps(ref_data, ensure_ascii=False),
            dumps_analysis_data=json.dumps(analysis_data, ensure_ascii=False),
            word_count=word_count,
        )

    @classmethod
    async def improve_score_prompt(
        cls,
        topic,
        title,
        original_content,
        improved_content,
        ref_data,
        analysis_data,
        level_score,
    ) -> str:
        """作文评分预估提示词"""
        prompt_template = await cls.get_prompt_template('improve_score')
        if not prompt_template:
            return ''

        return prompt_template.render(
            topic=topic,
            title=title,
            original_content=original_content,
            improved_content=improved_content,
            dumps_ref_data=json.dumps(ref_data, ensure_ascii=False),
            dumps_analysis_data=json.dumps(analysis_data, ensure_ascii=False),
            level_score=level_score,
        )

    @classmethod
    async def improve_summary_prompt(
        cls,
        original_content,
        improved_content,
        title,
        analysis_data,
    ) -> str:
        """作文优化总结提示词"""
        prompt_template = await cls.get_prompt_template('improve_summary')
        if not prompt_template:
            return ''

        return prompt_template.render(
            title=title,
            original_content=original_content,
            improved_content=improved_content,
            dumps_analysis_data=json.dumps(analysis_data, ensure_ascii=False),
        )

    @classmethod
    async def review_prompt(
        cls,
        topic,
        content,
        content_length,
        out_template,
        approximate,
        normal,
    ) -> str:
        """作文审核提示词"""
        prompt_template = await cls.get_prompt_template('review')
        if not prompt_template:
            return ''

        return prompt_template.render(
            topic=topic,
            content=content,
            content_length=content_length,
            out_template=out_template,
            approximate=approximate,
            normal=normal,
        )

    @classmethod
    async def improve_prompt(
        cls,
        topic,
        content,
        content_length,
        cl_normal,
        cl_max,
        level_score,
        ref_data,
    ) -> str:
        """作文优化提示词"""
        prompt_template = await cls.get_prompt_template('improve')
        if not prompt_template:
            return ''

        return prompt_template.render(
            topic=topic,
            content=content,
            content_length=content_length,
            cl_normal=cl_normal,
            cl_max=cl_max,
            level_score=level_score,
            ref_data=ref_data,
        )

    @classmethod
    async def compare_prompt(
        cls,
        level,
        topic,
        title,
        content,
        history_content,
        history_result,
    ) -> str:
        """作文比对提示词"""
        prompt_template = await cls.get_prompt_template('compare')
        if not prompt_template:
            return ''

        return prompt_template.render(
            level=level,
            topic=topic,
            title=title,
            content=content,
            history_content=history_content,
            history_result=history_result,
        )

    @classmethod
    async def ocr_system_prompt(cls) -> str:
        """广州团队使用的提示词"""
        prompt_template = await cls.get_prompt_template('ocr_system')
        if not prompt_template or not isinstance(prompt_template, Template):
            return ''

        return prompt_template.render()

    @classmethod
    async def ai_qa_prompt(cls, context, harmful, audit_description) -> str:
        """AI问答提示词"""
        prompt_template = await cls.get_prompt_template('ai_qa')
        if not prompt_template or not isinstance(prompt_template, Template):
            return ''

        return prompt_template.render(context=context, harmful=harmful, audit_description=audit_description)

    @staticmethod
    def wordcount_feedback_prompt(
        current_length: int,
        min_length: int,
        max_length: int,
    ) -> str:
        """生成字数优化反馈提示词"""

        feedback = ''
        if current_length < min_length:
            # 生成字数不足的反馈
            feedback = (
                f'当前字数为 {current_length}（不足 {min_length} 字）。'
                '请在不改变核心内容的前提下，增加细节、例子或修辞手法，严格确保字数达标！'
            )
        elif current_length > max_length:
            # 生成字数过多的反馈
            feedback = (
                f'当前字数为 {current_length}（超过 {max_length} 字）。请删减次要内容或简化表达，严格确保字数不超标！'
            )
        else:
            feedback = '当前字数符合要求，请直接输出内容。'

        return feedback
