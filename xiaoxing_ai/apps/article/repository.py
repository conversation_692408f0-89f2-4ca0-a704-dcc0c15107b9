import uuid
from typing import Sequence

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from xiaoxing_ai.db.aizuowen import QaReference
from xiaoxing_ai.db.models import (
    AiPrompt,
    Article,
    ArticleHomeworkImprovedRecords,
    ArticleHomeworkRecords,
    ArticleImproved,
)
from xiaoxing_ai.db.repository import CommonRepository


class ArticleRepository(CommonRepository):
    app_name = 'article'

    async def get_article_by_uid(self, uid: uuid.UUID | str) -> Article | None:
        stmt = select(Article).where(Article.uid == str(uid))
        q = await self.db.execute(stmt)
        instance = q.scalars().first()

        return instance

    async def create_article(
        self,
        uid: str,
        topic: str,
        content: str,
        level: str,
        grade: int | None = None,
    ) -> Article:
        """创建作文记录"""
        obj = Article(uid=uid, topic=topic, content=content, level=level)
        if grade:
            obj.extra = {'grade': grade}

        self.db.add(obj)
        await self.db.commit()
        return obj

    async def update_article_result(self, uid: str, result: dict):
        """更新作文评分结果"""
        stmt = select(Article).where(Article.uid == str(uid))
        q = await self.db.execute(stmt)
        instance = q.scalars().first()
        if instance:
            instance.result = result
            await self.db.commit()
        return instance

    async def get_article_improved_by_a_uid(self, a_uid: str) -> ArticleImproved | None:
        stmt = select(ArticleImproved).where(
            ArticleImproved.a_uid == a_uid,
            ArticleImproved.result.is_not(None),  # 过滤掉结果为null的
        )
        q = await self.db.execute(stmt)
        instance = q.scalars().first()

        return instance

    async def create_article_improved(
        self,
        review_id: str,
        source_content: str,
        content: str,
        summary: str | None = None,
        result: dict | None = None,
        uid: str | None = None,
    ):
        obj = ArticleImproved(
            uid=uid or str(uuid.uuid4()),  # 如果没有提供 uid，则生成一个新的
            a_uid=review_id,
            source_content=source_content,
            content=content,
            summary=summary,
            result=result,
        )
        self.db.add(obj)
        await self.db.commit()
        return obj

    async def get_article_improved_by_uid(self, uid: uuid.UUID | str) -> ArticleImproved | None:
        """获取优化作文记录"""
        stmt = select(ArticleImproved).where(ArticleImproved.uid == str(uid))
        q = await self.db.execute(stmt)
        instance = q.scalars().first()

        return instance

    async def update_article_improved_result(self, uid: str, status: int, result: dict):
        """更新作文优化评分结果"""
        stmt = select(ArticleImproved).where(ArticleImproved.uid == str(uid))
        q = await self.db.execute(stmt)
        instance = q.scalars().first()
        if instance:
            instance.result_task_status = status
            instance.result = result
            await self.db.commit()
        return instance


class HomeworkRepository:
    app_name = 'article'

    def __init__(self, db: AsyncSession) -> None:
        self.db = db

    async def get_model_prompt(self, prompt_name: str) -> str | None:
        stmt = select(AiPrompt.prompt).where(
            AiPrompt.app_name == self.app_name,
            AiPrompt.name == prompt_name,
            AiPrompt.deleted_at.is_(None),
        )

        q = await self.db.execute(stmt)
        return q.scalar_one_or_none()

    async def create_homework_record(self, uid: str, params: dict, extra: dict) -> ArticleHomeworkRecords:
        obj = ArticleHomeworkRecords(
            uid=uid,
            params=params,
            extra=extra,
        )

        self.db.add(obj)
        await self.db.commit()
        return obj

    async def get_homework_record(self, uid: str) -> ArticleHomeworkRecords | None:
        stmt = select(ArticleHomeworkRecords).where(ArticleHomeworkRecords.uid == uid)

        q = await self.db.execute(stmt)
        return q.scalar_one_or_none()

    async def create_homework_improved(self, uid: str, ahr_uid: str, content: str) -> ArticleHomeworkImprovedRecords:
        obj = ArticleHomeworkImprovedRecords(
            uid=uid,
            ahr_uid=ahr_uid,
            content=content,
        )

        self.db.add(obj)
        await self.db.commit()
        return obj


class GzRepository:
    """广州侧 数据库"""

    def __init__(self, db: AsyncSession) -> None:
        self.db = db

    async def get_qa_content(self, title: str) -> Sequence[dict[str, str]]:
        stmt = select(QaReference.question, QaReference.answer).where(QaReference.title == title)

        q = await self.db.execute(stmt)
        data = q.mappings().all()
        return [dict(q) for q in data]
