import datetime
import json
import math
import random
import re
from pathlib import Path

import structlog
from pydantic import BaseModel

from xiaoxing_ai.db import redis_client
from xiaoxing_ai.shared.helper import md5

from ...shared.milvus import MilvusVectorSearch
from . import HeaderState
from .agents.tools import ScoreCalculator
from .constants import REVIEW_CACHE_KEY, Grade, Level
from .dto import ChuzhongReviewSchema, GaozhongReviewSchema, ReviewOut, XiaoxueReviewSchema

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class ArticleService:
    @staticmethod
    def read_json_from_file(filepath) -> dict:
        """
        从指定的 JSON 文件中读取数据并返回 Python 对象。
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except FileNotFoundError:
            raise ValueError(f"错误: 文件 '{filepath}' 未找到。")
        except json.JSONDecodeError:
            raise ValueError(f"错误: 文件 '{filepath}' 不是有效的 JSON 格式。")
        except Exception as e:
            raise ValueError(f'读取文件时发生意外错误: {e}')

    @classmethod
    def get_default_result(cls, level: Level) -> dict:
        """获取默认的年级数据"""
        key = level.name.lower()

        current_file_path = Path(__file__).resolve()
        file_path = current_file_path.parent.joinpath(f'templates/{key}.json')

        return cls.read_json_from_file(file_path)

    @staticmethod
    def calc_content_length(content: str) -> int:
        """
        计算作文长度

        使用正则表达式替换所有空白字符后计算长度
        """

        content_length = len(re.sub(r'\s', '', content))
        return content_length

    @classmethod
    def check_is_minimum(cls, content: str, level: Level, grade: Grade | None) -> tuple[bool, dict]:
        """判断是满足年级指定的最小字数, 即不走模型判断"""
        word_count = level.get_word_count(grade)
        result = cls.get_default_result(level)

        content_length = cls.calc_content_length(content)

        if content_length < word_count.less:
            result['total_score'] = 2
            return True, result
        elif content_length < word_count.more:
            min_score, max_score = 3, 10

            score_range = max_score - min_score
            score_diff = word_count.more - word_count.less

            ratio = (content_length - word_count.less) / score_diff
            score = max(min_score, min_score + int(ratio * score_range))

            score = random.randint(min_score, score)
            result['total_score'] = score
            return True, result

        return False, result

    @classmethod
    def check_is_limit(cls, content: str, level: Level, grade: Grade | None) -> bool:
        word_count = level.get_word_count(grade)
        content_length = cls.calc_content_length(content)

        if content_length > word_count.similar:
            return True

        return False

    @classmethod
    def check_word_count(cls, content: str, level: Level, grade: Grade | None) -> tuple[bool, bool, dict]:
        """
        检测是否满足字数要求
        Returns:
            tuple:
                - is_minimum: bool 是否是最小字数要求
                - is_limit: bool 是否满足重新评分要求
                - check_result: dict 默认结果
        """

        is_minimum, check_result = cls.check_is_minimum(content, level, grade)
        is_limit = cls.check_is_limit(content, level, grade)

        return is_minimum, is_limit, check_result

    @classmethod
    def convert_english_punctuation_to_chinese(cls, text):
        """转换常见英文半角标点为中文全角标点，其他字符保持不变"""
        half_to_full = {
            ',': '，',
            '.': '。',
            '?': '？',
            '!': '！',
            ':': '：',
            ';': '；',
            '(': '（',
            ')': '）',
            '[': '【',
            ']': '】',
            '{': '｛',
            '}': '｝',
            '<': '《',
            '>': '》',
            '-': '－',
            '_': '＿',
        }

        result = []
        double_quote_open = True
        single_quote_open = True

        for char in text:
            if char == '"':
                result.append('“' if double_quote_open else '”')
                double_quote_open = not double_quote_open
            elif char == "'":
                result.append('‘' if single_quote_open else '’')
                single_quote_open = not single_quote_open
            else:
                result.append(half_to_full.get(char, char))

        return ''.join(result)

    @classmethod
    def compare_handler(
        cls,
        compare_res: dict,
        history_result: dict,
        normal: int,
        content_length: int,
    ) -> ReviewOut:
        """处理比较后的数据结构"""
        history_result = history_result.copy()
        detail_data = history_result.get('detail', {})

        cl_diff = math.ceil((normal - content_length) / 50)

        match compare_res['leval']:
            case Level.XIAOXUE:
                schema = XiaoxueReviewSchema
                calc_func = ScoreCalculator.calculate_xiaoxue_score
                score = cl_diff * 1
            case Level.CHUZHONG:
                schema = ChuzhongReviewSchema
                calc_func = ScoreCalculator.calculate_chuzhong_score
                score = cl_diff * 3
            case Level.GAOZHONG:
                schema = GaozhongReviewSchema
                calc_func = ScoreCalculator.calculate_gaozhong_score
                score = cl_diff * 3
            case _:
                raise
        h_detail = schema(**detail_data)

        # 先减去±4分
        for idx in compare_res['index']:
            key = idx['key']
            value = idx['value']
            if idx.get('type') == 'score':
                cls.update_model_value(h_detail, key, 'score', value)
            if idx.get('type') == 'summary':
                cls.update_model_value(h_detail, key, 'summary', value)
        # 覆盖历史评分
        history_result['detail'] = h_detail.model_dump()

        if cl_diff > 0:
            # 再通过ScoreCalculator, 分配字数分
            data = calc_func(h_detail.model_dump_json(), json.dumps({'total_deduction': score}))
            data.pop('calculation_process')
            # 替换历史数据
            history_result.update(data)

        result = ReviewOut(**history_result)
        result.summary = compare_res['total_summery']

        return result

    @classmethod
    def update_model_value(cls, model: BaseModel, key: str, attr: str, value):
        """
        递归地更新模型实例中指定键（key）对应的属性（attr）的值。
        只适用于作文评分模型
        参数:
        model -- 要更新的模型实例（必须是 BaseModel 类型）
        key -- 要查找的键
        attr -- 要更新的属性名
        value -- 要设置的新值
        """
        # 遍历模型实例的属性
        for current_key, current_value in model.__dict__.items():
            if current_key == key:
                # 找到目标键，更新其属性值
                if not hasattr(current_value, attr):
                    continue
                item = getattr(current_value, attr)
                # 如果是数字则执行加法
                if isinstance(item, (int, float)):
                    setattr(current_value, attr, item + value)
                    return
                # 否则直接替换
                setattr(current_value, attr, value)

            # 如果当前值是 BaseModel 类型，递归调用
            if isinstance(current_value, BaseModel):
                cls.update_model_value(current_value, key, attr, value)

    @staticmethod
    def get_cache_key(
        header: HeaderState,
        level: str,
        date: datetime.datetime | datetime.date | None = None,
    ) -> str:
        """获取缓存key"""
        if date is None:
            date = datetime.date.today()

        key = REVIEW_CACHE_KEY.format(
            date=date.isoformat(),
            md5_key=md5({'user_id': header.user_id, 'level': level}),
        )
        return key

    @classmethod
    async def get_cache_uids(cls, header: HeaderState, level: str) -> list[str]:
        """查出含今天日期-2 ~ 今天的数据, 返回3天内的所以缓存key"""

        date_list = [datetime.date.today() - datetime.timedelta(days=i) for i in reversed(range(3))]

        # 查询出设备今天的key
        cache_key_list = [cls.get_cache_key(header, level, date) for date in date_list]

        uids: list = [
            await redis_client.lrange(cache_key, 0, -1)  # pyright: ignore[reportGeneralTypeIssues]
            for cache_key in cache_key_list
        ]

        uids = sum(uids, [])

        return uids

    @classmethod
    async def get_similar_result(
        cls,
        header: HeaderState,
        level: str,
        content: str,
    ) -> tuple[bool, bool, str | None, dict | None]:
        """
        获取相似度数据
        Returns:
            tuple:
                - is_equal: bool 是否与原作文相等(>99%且字数在±5%以内)
                - is_similar: bool 是否与原作文相似(>90%)
                - best_content: bool 相似的作文内容
                - best_result: dict 相似的作文评价

        """
        is_equal = False  # 内容相等?
        is_similar = False  # 是否相似

        # 将学段也放入key中, 不然存在同一篇作文在不同学段,导致获取到的评价结构不一致
        uids = await cls.get_cache_uids(header, level)
        if not uids:
            return is_equal, is_similar, None, None

        # 在Milvus中根据uids搜索相似文章
        try:
            async with MilvusVectorSearch() as milvus:
                similar_articles = await milvus.search_similar_article_by_uids(content, uids, top_k=1)
                if not similar_articles:
                    return is_equal, is_similar, None, None
                _, best_score, best_content, best_result = similar_articles[0]
                content_length = cls.calc_content_length(content)
                best_content_length = cls.calc_content_length(best_content)
                # 计算绝对值
                length_diff_ratio = abs(content_length - best_content_length) / len(best_content)

                # 检查是否完全一样,还需判断字数差是否在5%以内,
                if best_score >= 0.99 and length_diff_ratio < 0.05:
                    is_equal = True
                elif best_score >= 0.90:  # 设置相似度阈值为0.90
                    is_similar = True

                return is_equal, is_similar, best_content, best_result
        except Exception as e:
            # 搜索失败时，继续执行后续逻辑
            await logger.aexception('Milvus搜索失败', exc_info=e)
            return is_equal, is_similar, None, None
