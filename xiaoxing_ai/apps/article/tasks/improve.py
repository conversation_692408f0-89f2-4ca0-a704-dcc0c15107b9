import structlog
from langfuse.decorators import langfuse_context, observe

from xiaoxing_ai.db import AsyncSessionLocal, redis_client
from xiaoxing_ai.settings import settings
from xiaoxing_ai.shared.llms import LLMConfig, get_llm
from xiaoxing_ai.shared.milvus import MilvusVectorSearch

from ..agents.enhanced_evaluator import EnhancedEvaluator
from ..agents.review_workflow import InputState, ReviewWorkflow
from ..constants import Grade, ImprovedStatus, Level
from ..dto import Progress, ReviewOut
from ..repository import ArticleRepository

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class ImproverTask:
    @staticmethod
    @observe(capture_output=False)
    async def improved_content_evaluate(
        llm_config: LLMConfig,
        uid: str,
        cache_key: str,
        topic: str,
        content: str,
        content_length: int,
        level: Level,
        grade: Grade | None,
    ):
        await logger.ainfo(f'开始优化后作文评分任务: {uid}')

        evaluator = EnhancedEvaluator()
        evaluate_result = ''

        # 3. 优化后的作文重新评分
        async with get_llm(llm_config) as llm, AsyncSessionLocal() as db:  # 确保 LLM、db 资源释放
            repo = ArticleRepository(db)

            try:
                state = InputState(
                    uid=uid,
                    topic=topic,
                    title=topic,
                    content=content,
                    content_length=content_length,
                    grade=grade,
                    level=level,
                    llm_config=llm_config,
                    llm=llm,
                )

                async for chunk in ReviewWorkflow().execute(state):  # pyright: ignore[reportGeneralTypeIssues]
                    if not isinstance(chunk, Progress):
                        # 只取最终结果转换为ReviewOut
                        evaluate_result = chunk

                review_out_result: ReviewOut = evaluator.format_review_result(evaluate_result, level, uid)

                # 更新
                await repo.update_article_improved_result(
                    uid=uid,
                    status=ImprovedStatus.SUCCESS.value,
                    result=review_out_result.model_dump() if review_out_result else review_out_result,
                )

                await redis_client.rpush(cache_key, uid)  # pyright: ignore[reportGeneralTypeIssues]
                await redis_client.expire(cache_key, settings.ARTICLE_CACHE_EXPIRE_SECONDS)
                # 将优化后的文章和结果插入到Milvus中
                async with MilvusVectorSearch() as milvus:
                    await milvus.insert_article(uid, content, review_out_result.model_dump_json())

                langfuse_context.update_current_observation(output=review_out_result.model_dump())

                await logger.ainfo(f'优化后作文评分完成: {uid}')

            except Exception as e:
                await logger.aerror(f'优化后作文评分失败: {uid}', exc_info=e)
                await repo.update_article_improved_result(
                    uid=uid,
                    status=ImprovedStatus.FAILED.value,
                    result={},
                )
