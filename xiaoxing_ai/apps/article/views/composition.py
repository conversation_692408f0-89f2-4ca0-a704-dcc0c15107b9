import asyncio
import json
import uuid

import structlog
from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile
from fastapi.responses import StreamingResponse
from langfuse.decorators import langfuse_context, observe
from openai.types.chat import (
    ChatCompletionChunk,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)
from sqlalchemy.ext.asyncio import AsyncSession

from xiaoxing_ai.consts import ChatEventType
from xiaoxing_ai.db import AsyncSessionLocal, get_db, get_gz_db, redis_client
from xiaoxing_ai.db.models import Article
from xiaoxing_ai.settings import settings
from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.api import ApiResponse
from xiaoxing_ai.shared.baidu import BaiduOCR
from xiaoxing_ai.shared.helper import (
    count_effective_chars,
    remove_blank_characters,
)
from xiaoxing_ai.shared.llms import LLMConfig, get_llm, llm_chat
from xiaoxing_ai.shared.milvus import MilvusVectorSearch
from xiaoxing_ai.shared.sse import make_sse_event
from xiaoxing_ai.shared.xfyun import XFAuditService

from .. import HeaderDep
from ..agents.enhanced_evaluator import EnhancedEvaluator
from ..agents.enhanced_improver import EnhancedImprover
from ..agents.evaluator import Evaluator
from ..constants import Grade, Level
from ..diff import get_diff
from ..dto import (
    ChuzhongReviewSchema,
    DiffIn,
    DiffOut,
    EnhanceImproveOut,
    GaozhongReviewSchema,
    ImproveOut,
    OCRRefineIn,
    OCRRefineOut,
    Progress,
    QAIn,
    QAMessage,
    ReviewOut,
    Summary,
    XiaoxueReviewSchema,
)
from ..prompts import PromptTemplates
from ..repository import ArticleRepository, GzRepository
from ..services import ArticleService

logger = structlog.stdlib.get_logger('xiaoxing_ai')
router = APIRouter(prefix='')
evaluator = EnhancedEvaluator()


@router.post('/enhance-review', summary='增强型作文评分批改', response_model=ReviewOut)
@observe(capture_input=False)
async def enhanced_review(
    header: HeaderDep,
    topic: str = Form(..., description='作文标题/题目/类型'),
    article: str | None = Form(None, description='用户上传的作文内容'),
    level: Level = Form(Level.GAOZHONG, description='作文等级', examples=['小学', '初中', '高中']),
    grade: Grade | None = Form(None, description='小学的年级', examples=['3']),
    image: UploadFile | None = File(None, description='作文图片, 文件大小需小于5M'),
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    """
    使用ReAct方式的增强型作文批改接口

    作文内容和图片参数同时传入，则优先使用更为精确的作文内容
    """

    if all([article is None, image is None]):
        raise HTTPException(status_code=400, detail='作文内容和图片不能同时为空')

    # 获取AI模型实例数据
    repo = ArticleRepository(db)
    llm_config = await repo.get_ai_config_by_module('article:enhance-review')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    if article and article.strip():
        content = article
    elif image:
        file_data = await image.read()

        if len(file_data) > 1024 * 1024 * 5:
            raise HTTPException(status_code=400, detail='识别图片体积最大不能超过5MB')

        content = await BaiduOCR().read(file_data)
    else:
        raise HTTPException(status_code=400, detail='需要提供有效的作文内容或图片')

    if level == Level.XIAOXUE and grade is None:
        raise HTTPException(status_code=400, detail='grade 字段不能为空')

    # 创建作文记录
    repository = ArticleRepository(db)
    uid = str(uuid.uuid4())
    langfuse_context.update_current_trace(session_id=uid)

    await repository.create_article(
        uid=uid,
        topic=topic,
        content=content,
        level=level.value,
        grade=grade,
    )

    # 提取标题
    topic_list = topic.split('\n')
    topic, title = topic_list[0], topic_list[-1]

    svc = ArticleService()

    # 去除作文中间出现的空格,避免影响标点符号扣分步骤评分
    new_content = remove_blank_characters(content)
    new_content = svc.convert_english_punctuation_to_chinese(new_content)
    content_length = len(new_content)

    # 先进行作文长度判断
    is_minimum, is_limit, check_result = svc.check_word_count(new_content, level, grade)
    if is_minimum:
        out = ReviewOut(**check_result, review_id=uid)
        # 保存结果到数据库
        await repository.update_article_result(uid=uid, result=out.model_dump())

        def cache_result():
            yield make_sse_event(out)

        return StreamingResponse(
            cache_result(),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )

    cache_key = svc.get_cache_key(header, level.value)

    # 大于限制字数进行匹配相似度
    if is_limit:
        is_equal = False  # 内容相等?
        is_similar = False  # 是否相似
        best_content = None  # 相似度最高的内容
        best_result = None  # 相似度最高的评分结果

        # 将学段也放入key中, 不然存在同一篇作文在不同学段,导致获取到的评价结构不一致
        uids = await svc.get_cache_uids(header, level.value)
        if uids:
            # 在Milvus中根据uids搜索相似文章
            try:
                async with MilvusVectorSearch() as milvus:
                    similar_articles = await milvus.search_similar_article_by_uids(content, uids, top_k=1)

                if similar_articles:
                    _, best_score, best_content, best_result = similar_articles[0]
                    best_content_length = count_effective_chars(best_content)
                    # 计算绝对值
                    length_diff_ratio = abs(content_length - best_content_length) / len(best_content)
                    # 检查是否完全一样,还需判断字数差是否在5%以内,
                    if best_score >= 0.99 and length_diff_ratio < 0.05:
                        is_equal = True
                    # 检查相似度分数是否达到阈值
                    elif best_score >= 0.90:  # 设置相似度阈值为0.90
                        is_similar = True
            except Exception as e:
                # 搜索失败时，继续执行后续逻辑
                await logger.aexception('Milvus搜索失败', exc_info=e)

        if best_content and best_result:
            # 完全相等的情况下直接返回原来的结果
            if is_equal:
                out = ReviewOut(**best_result)
                out.review_id = uid
                await repository.update_article_result(uid=uid, result=out.model_dump())

                @observe(capture_output=False)
                async def equal_result():
                    yield make_sse_event(out)

                return StreamingResponse(
                    equal_result(),
                    media_type='text/event-stream',
                    headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
                )

            # 相似的情况
            if is_similar:
                async with llms.get_llm(llm_config) as llm:
                    res = await evaluator.compare_review(
                        llm,
                        llm_config.model,
                        level=level,
                        topic=topic,
                        title=title,
                        content=content,
                        history_content=best_content,
                        history_result=best_result,
                    )
                normal = level.get_word_count(grade).normal
                result = svc.compare_handler(res, best_result, normal, content_length)
                result.review_id = uid

                langfuse_context.update_current_observation(output=result.model_dump())

                await repository.update_article_result(uid=uid, result=result.model_dump())

                # 将新文章和结果插入到Milvus中
                async with MilvusVectorSearch() as milvus:
                    await milvus.insert_article(uid, content, result.model_dump_json())

                await redis_client.rpush(cache_key, uid)  # pyright: ignore[reportGeneralTypeIssues]
                await redis_client.expire(cache_key, settings.ARTICLE_CACHE_EXPIRE_SECONDS)

                @observe(capture_output=False)
                async def compare_result():
                    yield make_sse_event(result)

                return StreamingResponse(
                    compare_result(),
                    media_type='text/event-stream',
                    headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
                )

    @observe(capture_output=False)
    async def generator(llm_config: LLMConfig):
        # 使用增强型评分器进行评分
        async with llms.get_llm(llm_config) as llm, AsyncSessionLocal() as db:
            repository = ArticleRepository(db)

            res = await evaluator.enhanced_review(
                llm=llm,
                model=llm_config.model,
                topic=topic,
                title=title,
                content=content,
                level=level,
                grade=grade,
                stream=True,
            )
            async for chunk in res:  # pyright: ignore[reportGeneralTypeIssues]
                # 区分进度消息和最终结果
                if 'progress' in chunk:
                    # 进度消息不需要符合ReviewOut结构
                    yield make_sse_event(chunk)
                else:
                    # 只有最终结果才转换为ReviewOut
                    _result = evaluator.format_review_result(chunk, level, uid)

                    # 最后更新数据库
                    await repository.update_article_result(uid=uid, result=_result.model_dump())
                    langfuse_context.update_current_observation(output=_result.model_dump())

                    # 将新文章和结果插入到Milvus中
                    async with MilvusVectorSearch() as milvus:
                        await milvus.insert_article(uid, content, _result.model_dump_json())

                    await redis_client.rpush(cache_key, uid)  # pyright: ignore[reportGeneralTypeIssues]
                    await redis_client.expire(cache_key, settings.ARTICLE_CACHE_EXPIRE_SECONDS)

                    yield make_sse_event(_result)

    return StreamingResponse(
        generator(llm_config),
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )


@router.post('/review', summary='作文评分批改')
async def review(
    topic: str = Form(..., description='作文标题/题目/类型'),
    article: str | None = Form(None, description='用户上传的作文内容'),
    level: Level = Form(Level.GAOZHONG, description='作文等级', examples=['小学', '初中', '高中']),
    grade: Grade | None = Form(None, description='小学的年级', examples=['3']),
    image: UploadFile | None = File(None, description='作文图片, 文件大小需小于5M'),
    db: AsyncSession = Depends(get_db),
) -> ApiResponse[ReviewOut]:
    """
    极速批改模式

    作文内容和图片参数同时传入，则优先使用更为精确的作文内容
    """
    if all([article is None, image is None]):
        raise HTTPException(status_code=400, detail='作文内容和图片不能同时为空')

    if article and article.strip():
        content = article
    elif image:
        file_data = await image.read()

        if len(file_data) > 1024 * 1024 * 5:
            raise HTTPException(status_code=400, detail='识别图片体积最大不能超过5MB')

        content = await BaiduOCR().read(file_data)
    else:
        raise HTTPException(status_code=400, detail='需要提供有效的作文内容或图片')

    if level == Level.XIAOXUE and grade is None:
        raise HTTPException(status_code=400, detail='grade 字段不能为空')

    uid = str(uuid.uuid4())
    obj = Article(uid=uid, topic=topic, content=content, level=level)
    if grade:
        obj.extra = {'grade': grade.value}

    db.add(obj)
    await db.commit()

    new_content = remove_blank_characters(content)
    # 长度判断
    is_minimum, _, check_result = ArticleService.check_word_count(new_content, level, grade)

    if is_minimum:
        resp = check_result
    else:
        # 获取AI模型实例数据
        repo = ArticleRepository(db)
        llm_config = await repo.get_ai_config_by_module('article:review')
        if not llm_config:
            raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')
        async with get_llm(llm_config) as llm:
            # 模型评分
            resp = await Evaluator().review(
                llm=llm,
                model=llm_config.model,
                topic=topic,
                content=content,
                level=level,
                grade=grade,
                uid=uid,
            )

    # 转为固定的格式
    try:
        res = resp.copy()
        detail_data = res.pop('detail')
        match level:
            case level.XIAOXUE:
                detail = XiaoxueReviewSchema(**detail_data)
            case level.CHUZHONG:
                detail = ChuzhongReviewSchema(**detail_data)
            case level.GAOZHONG:
                detail = GaozhongReviewSchema(**detail_data)
            case _:
                raise
        if is_minimum:
            res['total_score'] = sum([item.score for _, item in detail])
        result = ReviewOut(**res, review_id=uid, detail=detail)
    except Exception as e:
        await logger.aexception(f'评分失败: {e}', llm_resp=resp)
        raise HTTPException(status_code=500, detail='评分失败! 请重试')

    obj.result = result.model_dump()
    await db.commit()

    return ApiResponse(data=result)


@router.get('/improve', summary='作文内容优化')
@observe(capture_output=False)
async def get_improve(
    review_id: uuid.UUID = Query(..., description='批改接口所返回的对应UUID'),
    stream: int = Query(default=0, description='是否返回流', examples=[0, 1], ge=0, le=1),
    db: AsyncSession = Depends(get_db),
):
    """作文修改基于模型已给出的批改意见对文章进一步修改"""
    repo = ArticleRepository(db)

    instance = await repo.get_article_by_uid(review_id)
    if instance is None:
        raise HTTPException(status_code=404, detail='该review_id不存在')

    langfuse_context.update_current_trace(session_id=instance.uid)

    # 获取AI模型实例数据
    llm_config = await repo.get_ai_config_by_module('article:improve')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    async with get_llm(llm_config) as llm:
        res = await Evaluator().improve(
            llm=llm,
            model=llm_config.model,
            topic=instance.topic,
            content=instance.content,
            ref_data=instance.result or {},
            level=Level(instance.level),
            grade=instance.extra.get('grade') if instance.extra else None,
            stream=bool(stream),
        )

    if not bool(stream):
        # 非流式响应直接保存数据库
        await repo.create_article_improved(
            review_id=instance.uid,
            source_content=instance.content,
            content=res.get('content', ''),  # pyright: ignore[reportAttributeAccessIssue]
            uid=str(uuid.uuid4()),  # 添加生成的 uid
        )
        return ApiResponse(data=res)

    @observe(capture_output=False)
    async def generator():
        buffer = []
        total_score: int = 0
        try:
            async for chunk in res:  # type: ignore
                if not isinstance(chunk, ChatCompletionChunk):
                    total_score = chunk
                    continue

                if content := chunk.choices[0].delta.content:
                    buffer.append(content)

                if content is None:
                    continue

                out = ImproveOut(content=content, total_score=total_score)
                yield make_sse_event(out)

        except Exception as e:
            await logger.aexception('生成优化内容失败', exc_info=e)
            raise

        await repo.create_article_improved(
            review_id=str(instance.uid),
            source_content=str(instance.content),
            content=''.join(buffer),
            uid=str(uuid.uuid4()),
        )

    return StreamingResponse(
        generator(),
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )


@router.get('/enhance-improve', summary='增强型作文优化', response_model=EnhanceImproveOut)
@observe(capture_output=False)
async def enhanced_improve(
    header: HeaderDep,
    review_id: uuid.UUID = Query(..., description='批改接口所返回的对应UUID'),
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    """
    增强型作文修改

    使用ReAct方式基于模型已给出的批改意见对文章进行分析并优化
    四个步骤：
    1. 分析改进点
    2. 生成优化内容（实时流式返回）
    3. 预估优化后的分数
    4. 生成优化总结（实时流式返回）
    """
    repo = ArticleRepository(db)

    # 获取AI模型实例数据
    ai_configs = {
        'analysis': await repo.get_ai_config_by_module('article:enhance-improve_analysis'),
        'improve': await repo.get_ai_config_by_module('article:enhance-improve_content'),
        'eval': await repo.get_ai_config_by_module('article:enhance-improve_eval'),
        'summary': await repo.get_ai_config_by_module('article:enhance-improve_summary'),
    }

    # 校验配置
    if not all(ai_configs.values()):
        missing = [k for k, v in ai_configs.items() if not v]
        raise HTTPException(500, detail=f'未找到对应模块的 AI 配置: {missing}')

    instance = await repo.get_article_by_uid(review_id)
    if instance is None:
        raise HTTPException(status_code=404, detail='该review_id不存在')
    cache_key = ArticleService.get_cache_key(header, instance.level)

    langfuse_context.update_current_trace(session_id=instance.uid)

    # 获取作文级别和年级信息
    level = Level(instance.level)
    grade = Grade(instance.extra.get('grade')) if instance.extra and 'grade' in instance.extra else None

    # 提取标题
    topic_list = instance.topic.split('\n')
    topic, title = topic_list[0], topic_list[-1]

    if level == Level.XIAOXUE and grade is None:
        raise HTTPException(status_code=400, detail='小学作文优化必须提供年级信息')

    # 使用增强型优化器进行优化
    improver = EnhancedImprover()

    @observe(capture_output=False)
    async def generator(ai_configs: dict[str, LLMConfig]):
        # 准备数据
        original_content = instance.content
        # 去除作文中间出现的空格,避免影响标点符号扣分步骤评分
        new_content = remove_blank_characters(original_content)
        new_content = ArticleService.convert_english_punctuation_to_chinese(new_content)

        total_score = 0

        llm_config_analysis = ai_configs['analysis']
        async with get_llm(llm_config_analysis) as llm:
            # 1. 分析改进点
            yield make_sse_event(Progress(progress=10, step='正在分析作文不足点...'))
            analysis_data = await improver.react_improver.analyze_improvement(
                llm=llm,
                model=llm_config_analysis.model,
                temperature=llm_config_analysis.params.temperature,
                topic=topic,
                title=title,
                content=original_content,
                ref_data=instance.result,  # pyright: ignore[reportArgumentType]
            )
            yield make_sse_event(Progress(progress=25, step='分析完成，开始优化内容...'))

            # 2. 直接流式生成优化内容
            word_count = level.get_word_count(grade)
            content_length = count_effective_chars(new_content)
            level_score = level.get_score()

            # 生成内容提示词
            prompt = await PromptTemplates.improve_content_prompt(
                topic,
                title,
                original_content,
                instance.result,
                analysis_data,
                content_length,
                word_count,
                level_score,
            )

            llm_config_improve = ai_configs['improve']
            async with get_llm(llm_config_improve) as llm:
                # 使用真实流式API
                improved_content_buffer = []
                improved_params = llms.get_params(
                    llm_config=llm_config_improve,
                    messages=[{'role': 'system', 'content': prompt}],
                )

                res = await llms.llm_chat_stream(llm, improved_params)
                async for chunk in res:
                    if not chunk or not chunk.choices:
                        continue

                    chunk_content = chunk.choices[0].delta.content or ''
                    if not chunk_content:
                        continue

                    improved_content_buffer.append(chunk_content)
                    # 直接发送内容片段
                    out = EnhanceImproveOut(content=chunk_content, total_score=total_score, summary='')
                    yield make_sse_event(out)

                # 获取完整优化内容
                improved_content = ''.join(improved_content_buffer)

                yield make_sse_event(Progress(progress=60, step='内容优化完成，预估分数...'))

            # 优化后的作文重新评分
            llm_config_eval = ai_configs['eval']
            async with get_llm(llm_config_eval) as llm:
                review_task = asyncio.create_task(
                    evaluator.enhanced_review(  # pyright: ignore[reportAssignmentType]
                        llm=llm,
                        model=llm_config_eval.model,
                        topic=topic,
                        title=title,
                        content=improved_content,
                        level=level,
                        grade=grade,
                        stream=False,
                    )
                )

                while not review_task.done():
                    _out = EnhanceImproveOut(content='', total_score=0, summary='')
                    yield make_sse_event(_out)
                    await asyncio.sleep(1)

                improved_result: dict = await review_task  # pyright: ignore[reportAssignmentType,reportRedeclaration]
                total_score = improved_result.get('total_score', 0)  # pyright: ignore[reportAttributeAccessIssue]

                # 直接发送预估分数
                out = EnhanceImproveOut(content='', total_score=total_score, summary='')
                yield make_sse_event(out)

            # 4. 生成优化总结
            yield make_sse_event(Progress(progress=75, step='正在生成优化总结...'))
            # 生成总结提示词
            summary_prompt = await PromptTemplates.improve_summary_prompt(
                original_content, improved_content, title, analysis_data
            )

            llm_config_summary = ai_configs['summary']
            # 流式生成总结
            summary_buffer = []
            summary_params = llms.get_params(
                llm_config=llm_config_summary, messages=[{'role': 'system', 'content': summary_prompt}]
            )

            async with get_llm(llm_config_summary) as llm:
                try:
                    res = await llms.llm_chat_stream(llm, summary_params)
                    async for chunk in res:
                        if not chunk or not chunk.choices:
                            continue

                        chunk_content = chunk.choices[0].delta.content or ''
                        if not chunk_content:
                            continue
                        summary_buffer.append(chunk_content)
                        # 直接发送总结片段
                        out = EnhanceImproveOut(content='', total_score=0, summary=chunk_content)
                        yield make_sse_event(out)
                except Exception as e:
                    await logger.aerror(str(e))
                    yield make_sse_event(Progress(progress=90, step='生成总结失败'))

            summary = ''.join(summary_buffer)

            # 完成所有步骤
            yield make_sse_event(Progress(progress=100, step='优化完成'))

            uid = str(uuid.uuid4())  # 确保生成一个新的UUID

        improved_result: ReviewOut = evaluator.format_review_result(improved_result, level, uid)

        # 保存
        await repo.create_article_improved(
            review_id=str(instance.uid),
            source_content=str(instance.content),
            content=improved_content,
            summary=summary,
            result=improved_result.model_dump() if improved_result else improved_result,
            uid=uid,
        )

        await redis_client.rpush(cache_key, uid)  # pyright: ignore[reportGeneralTypeIssues]
        await redis_client.expire(cache_key, settings.ARTICLE_CACHE_EXPIRE_SECONDS)
        # 将优化后的文章和结果插入到Milvus中
        async with MilvusVectorSearch() as milvus:
            await milvus.insert_article(uid, improved_content, improved_result.model_dump_json())

        # 保存结果供trace_fuse使用
        final_result = {'total_score': total_score, 'content': improved_content, 'summary': summary}
        langfuse_context.update_current_observation(output=final_result)

    return StreamingResponse(
        generator(ai_configs),  # type: ignore[assignment]
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )


@router.get('/summary', summary='生成优化总结', responses={200: {'model': Summary}})
@observe(capture_output=False)
async def get_summary(
    review_id: uuid.UUID = Query(..., description='批改接口所返回的对应UUID'),
    stream: int = Query(default=0, description='是否返回流', examples=[0, 1], ge=0, le=1),
    db: AsyncSession = Depends(get_db),
):
    """
    提取生成优化总结的流程为单独接口
    """
    repo = ArticleRepository(db)
    # 获取AI模型实例数据
    llm_config = await repo.get_ai_config_by_module('article:summary')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    instance = await repo.get_article_by_uid(review_id)
    if instance is None:
        raise HTTPException(status_code=404, detail='该review_id不存在')

    improve = await repo.get_article_improved_by_a_uid(instance.uid)
    if improve is None:
        raise HTTPException(status_code=404, detail='不存在对应的优化记录')

    langfuse_context.update_current_trace(session_id=instance.uid)
    # 获取作文级别和年级信息
    level = Level(instance.level)
    grade = Grade(instance.extra.get('grade')) if instance.extra and 'grade' in instance.extra else None

    # 提取标题
    topic_list = instance.topic.split('\n')
    _, title = topic_list[0], topic_list[-1]

    if level == Level.XIAOXUE and grade is None:
        raise HTTPException(status_code=400, detail='小学作文优化必须提供年级信息')

    # 调用封装的 summary 方法
    if stream == 1:

        @observe(capture_output=False)
        async def generator(llm_config: LLMConfig):
            async with get_llm(llm_config) as llm:
                async for delta_content in await Evaluator().summary(
                    llm=llm,
                    model=llm_config.model,
                    content=str(instance.content),
                    improve_content=str(improve.content),
                    title=title,
                    analysis_data=instance.result or {},
                    stream=True,
                    temperature=llm_config.params.temperature,
                ):  # type: ignore
                    if content := delta_content.choices[0].delta.content:
                        out = Summary(summary=content)

                        yield make_sse_event(out)

        return StreamingResponse(
            generator(llm_config),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )
    else:
        async with get_llm(llm_config) as llm:
            summary = await Evaluator().summary(
                llm=llm,
                model=llm_config.model,
                content=instance.content,
                improve_content=improve.content,
                title=title,
                analysis_data=instance.result or {},
                stream=False,
                temperature=llm_config.params.temperature,
            )

        # 返回生成的总结
        return ApiResponse(data=Summary(summary=summary))


@router.post('/ocr-refine', summary='OCR结果优化')
async def ocr_refine(data: OCRRefineIn, db: AsyncSession = Depends(get_db)) -> ApiResponse[OCRRefineOut]:
    """将百度ocr识别的结果 优化成可读的段落"""

    # 获取AI模型实例数据
    repo = ArticleRepository(db)
    llm_config = await repo.get_ai_config_by_module('article:ocr-refine')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    try:
        prompt = json.dumps(data.words_result, ensure_ascii=False)
    except Exception as e:
        await logger.ainfo(f'err: {e}')
        raise HTTPException(status_code=400, detail='非法JSON字符串')

    content = await PromptTemplates.ocr_system_prompt()
    if not content or not isinstance(content, str):
        raise HTTPException(status_code=500, detail='提示词无效')

    messages = [
        ChatCompletionSystemMessageParam(content=content, role='system'),
        ChatCompletionUserMessageParam(content=prompt, role='user'),
    ]
    params = llms.get_params(llm_config=llm_config, messages=messages)

    try:
        async with get_llm(llm_config) as llm:
            content: str = await llm_chat(llm, params)
    except ValueError:
        raise HTTPException(status_code=500, detail='段落优化失败，请稍后重试')

    return ApiResponse(data={'content': content})


@router.post('/qa', summary='知识问答')
@observe(capture_input=False)
async def get_qa(
    data: QAIn,
    db: AsyncSession = Depends(get_db),
    gz_db: AsyncSession = Depends(get_gz_db),
) -> StreamingResponse:
    """知识问答接口(流式返回)"""
    # 处理流程:
    # 1. 检查用户问题是否为空
    # 2. 获取或创建会话:
    #    - 如果提供了session_id且存在，使用现有会话
    #    - 否则创建新会话
    # 3. 获取会话历史记录
    # 4. 调用LLM生成回答
    # 5. 更新会话历史
    # 6. 流式返回结果

    repo = ArticleRepository(db)
    gz_repo = GzRepository(gz_db)

    repo = ArticleRepository(db)
    llm_config = await repo.get_ai_config_by_module('article:qa')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    if not data.query.strip():
        raise HTTPException(status_code=400, detail='问题不能为空')

    session_id = await repo.get_or_create_session(data.session_id)
    if not session_id:
        raise HTTPException(status_code=404, detail=f'会话{data.session_id}不存在')

    langfuse_context.update_current_trace(session_id=session_id)
    history = await repo.get_chat_history(session_id=session_id, n=8)
    qa_content = await gz_repo.get_qa_content(title=data.title)

    harmful, audit_description = await XFAuditService().audit(data.query)

    context = [
        dict(
            role='system',
            content=await PromptTemplates.ai_qa_prompt(
                context=qa_content or '无',
                harmful=harmful,
                audit_description=audit_description,
            ),
        )
    ]
    for msg in history:
        context.append(dict(role='user', content=msg.input))
        context.append(dict(role='assistant', content=msg.output))

    params = llms.get_params(llm_config=llm_config, messages=[*context, dict(role='user', content=data.query)])

    @observe(as_type='generation', capture_output=False)
    async def generator(llm_config: LLMConfig, params):
        buffer = []

        async with get_llm(llm_config) as llm:
            r = await llms.llm_chat_stream(llm, params)  # type: ignore
            async for chunk in r:
                if content := chunk.choices[0].delta.content:
                    buffer.append(content)

                    msg = QAMessage(session_id=session_id, content=content)

                    # 豆包的 finish_reason 定义
                    # https://api.volcengine.com/api-docs/view?action=ChatCompletionsText&serviceCode=ark&version=2024-01-01#%E5%93%8D%E5%BA%94%E5%8F%82%E6%95%B0
                    # deepseek finish_reason 定义
                    # https://api-docs.deepseek.com/zh-cn/api/create-chat-completion#responses
                    if chunk.choices[0].finish_reason == 'content_filter':
                        content = '抱歉，不如我们再换个话题聊聊吧~'
                        msg.content = content
                        msg.type = ChatEventType.CLEAR

                    yield make_sse_event(msg)

                    # 当检测到是最后一个chunk时，保存消息
                    if chunk.choices[0].finish_reason == 'stop':
                        await repo.create_message(session_id=session_id, input=data.query, content=''.join(buffer))

        langfuse_context.update_current_observation(output=''.join(buffer))

    try:
        return StreamingResponse(
            generator(llm_config, params),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )
    except Exception as e:
        await logger.aexception('Failed to generate response', exc_info=e)
        raise HTTPException(status_code=500, detail='Failed to generate response')


@router.post('/diff', summary='文本对比')
async def diff(
    data: DiffIn,
    is_html: bool = Query(False, description='是否返回html格式'),
) -> ApiResponse[DiffOut | str]:
    """
    is_html 为真会返回由框架生成的html格式的文本
    """
    result = get_diff(data.old_text, data.new_text, is_html=is_html)

    return ApiResponse(data=result)
