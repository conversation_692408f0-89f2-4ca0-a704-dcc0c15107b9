import uuid
from copy import deepcopy

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query
from langfuse.decorators import langfuse_context, observe
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import StreamingResponse

from xiaoxing_ai.db import get_db
from xiaoxing_ai.shared.api import ApiResponse

from ..agents.homework import Evaluator
from ..dto.homework import EvaluationIn, EvaluationOut, ImprovedIn
from ..repository import HomeworkRepository

logger = structlog.stdlib.get_logger('xiaoxing_ai')
router = APIRouter(prefix='/homework')


@router.post('/evaluation', summary='作业评价')
@observe(capture_input=False)
async def homework_evaluation(
    data: EvaluationIn,
    db: AsyncSession = Depends(get_db),
) -> ApiResponse[EvaluationOut]:
    repo = HomeworkRepository(db)
    uid = str(uuid.uuid4())
    langfuse_context.update_current_trace(session_id=uid)

    # 拆分传入的参数
    params = data.model_dump()
    extra = {
        'level': params.pop('level'),
        'grade': params.pop('grade', None),
    }
    instance = await repo.create_homework_record(uid, params, extra)

    template = await repo.get_model_prompt('homework_evaluation')
    if not template:
        logger.error('不存在 homework_evaluation 提示词模板')
        raise HTTPException(status_code=500, detail='作业评价失败')

    res = await Evaluator.get_evaluation(template, data)

    instance.result = res
    await db.commit()

    res['uuid'] = uid

    return ApiResponse(data=res)


@router.get('/improved', summary='作业优化')
@observe(capture_input=False)
async def homework_improved(
    uid: uuid.UUID = Query(..., description='作业评价返回的uid'),
    db: AsyncSession = Depends(get_db),
):
    repo = HomeworkRepository(db)
    improved_uid = str(uuid.uuid4())
    langfuse_context.update_current_trace(session_id=improved_uid)

    record = await repo.get_homework_record(str(uid))
    if not record:
        raise HTTPException(status_code=404, detail='未查询对应的数据')

    data = ImprovedIn(**record.params, **(record.extra or {}))

    template = await repo.get_model_prompt('homework_improved')
    if not template:
        logger.error('不存在 homework_improved 提示词模板')
        raise HTTPException(status_code=500, detail='作业优化失败')

    eval_template = await repo.get_model_prompt('homework_evaluation')
    if not eval_template:
        logger.error('不存在 homework_evaluation 提示词模板')
        raise HTTPException(status_code=500, detail='作业优化失败')

    evaluator = Evaluator()

    @observe(capture_input=False)
    async def generator():
        res = await evaluator.get_improved(template, data, record.result or {})

        buffer = []
        async for chunk in res:
            if not chunk or not chunk.choices:
                continue

            chunk_content = chunk.choices[0].delta.content or ''
            if not chunk_content:
                continue

            buffer.append(chunk_content)
            # 直接发送内容片段
            yield f'data: {chunk_content}\n\n'

        content = ''.join(buffer)

        instance = await repo.create_homework_improved(improved_uid, str(uid), content)

        # 构造评价数据
        eval_data = deepcopy(data)
        eval_data.content = content

        eval_res = await evaluator.get_evaluation(eval_template, eval_data)

        instance.result = eval_res
        await db.commit()

        out = EvaluationOut(**eval_res, uuid=improved_uid)

        yield f'data: {out.model_dump_json()}\n\n'

    return StreamingResponse(
        generator(),
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )
