import asyncio
import uuid
from datetime import datetime, timedelta

import structlog
from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile
from fastapi.responses import StreamingResponse
from langfuse.decorators import langfuse_context, observe
from sqlalchemy.ext.asyncio.session import AsyncSession

from xiaoxing_ai.db import get_db, redis_client
from xiaoxing_ai.settings import settings
from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.api import ApiResponse
from xiaoxing_ai.shared.baidu import BaiduOCR
from xiaoxing_ai.shared.helper import count_effective_chars, remove_blank_characters
from xiaoxing_ai.shared.llms import LLMConfig, get_llm
from xiaoxing_ai.shared.milvus import MilvusVectorSearch
from xiaoxing_ai.shared.sse import make_sse_event

from ... import HeaderDep
from ...agents.enhanced_evaluator import EnhancedEvaluator
from ...agents.react_agents import ReactImprover
from ...agents.review_workflow import InputState, ReviewWorkflow
from ...constants import Grade, ImprovedStatus, Level
from ...dto import ImprovedProcessResult, ImprovedResultOut, Progress, ReviewOut
from ...prompts import PromptTemplates
from ...repository import ArticleRepository
from ...services import ArticleService
from ...tasks.improve import ImproverTask

logger = structlog.stdlib.get_logger('xiaoxing_ai')
router = APIRouter(prefix='')

improver = ReactImprover()


@router.post('/review', summary='作文评分批改v2', response_model=ReviewOut)
@observe(capture_input=False)
async def review_v2(
    header: HeaderDep,
    topic: str = Form(..., description='作文标题/题目/类型'),
    article: str | None = Form(None, description='用户上传的作文内容'),
    level: Level = Form(Level.GAOZHONG, description='作文等级', examples=['小学', '初中', '高中']),
    grade: Grade | None = Form(None, description='小学的年级', examples=['3']),
    image: UploadFile | None = File(None, description='作文图片, 文件大小需小于5M'),
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    """
    作文内容和图片参数同时传入，则优先使用更为精确的作文内容
    """

    if all([article is None, image is None]):
        raise HTTPException(status_code=400, detail='作文内容和图片不能同时为空')

    if article and article.strip():
        content = article
    elif image:
        file_data = await image.read()

        if len(file_data) > 1024 * 1024 * 5:
            raise HTTPException(status_code=400, detail='识别图片体积最大不能超过5MB')

        content = await BaiduOCR().read(file_data)
    else:
        raise HTTPException(status_code=400, detail='需要提供有效的作文内容或图片')

    if level == Level.XIAOXUE and grade is None:
        raise HTTPException(status_code=400, detail='grade 字段不能为空')

    # 创建作文记录
    evaluator = EnhancedEvaluator()
    uid = str(uuid.uuid4())
    langfuse_context.update_current_trace(session_id=uid)
    # 获取AI模型实例数据
    repo = ArticleRepository(db)
    llm_config = await repo.get_ai_config_by_module('article:review:v2')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    await repo.create_article(
        uid=uid,
        topic=topic,
        content=content,
        level=level,
        grade=grade,
    )

    # 提取标题
    topic_list = topic.split('\n')
    topic, title = topic_list[0], topic_list[-1]

    svc = ArticleService()

    # 去除作文中间出现的空格,避免影响标点符号扣分步骤评分
    new_content = remove_blank_characters(content)
    new_content = svc.convert_english_punctuation_to_chinese(new_content)
    content_length = len(new_content)

    # 最少字数判断
    is_minimum, check_result = svc.check_is_minimum(new_content, level, grade)
    if is_minimum:
        out = ReviewOut(**check_result, review_id=uid)  # pyright: ignore[reportCallIssue]
        # 保存结果到数据库
        await repo.update_article_result(uid=uid, result=out.model_dump())

        def cache_result():
            yield make_sse_event(out)

        return StreamingResponse(
            cache_result(),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )

    cache_key = svc.get_cache_key(header, level)

    # 大于限制字数进行匹配相似度
    is_limit = svc.check_is_limit(new_content, level, grade)
    if is_limit:
        is_equal, is_similar, best_content, best_result = await svc.get_similar_result(header, level, content)

        if best_content and best_result:
            # 完全相等的情况下直接返回原来的结果
            if is_equal:
                out = ReviewOut(**best_result)
                out.review_id = uid
                await repo.update_article_result(uid=uid, result=out.model_dump())

                @observe(capture_output=False)
                async def equal_result():
                    yield make_sse_event(out)

                return StreamingResponse(
                    equal_result(),
                    media_type='text/event-stream',
                    headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
                )

            # 相似的情况
            if is_similar:
                async with llms.get_llm(llm_config) as llm:
                    res = await evaluator.compare_review(
                        llm,
                        llm_config.model,
                        level=level,
                        topic=topic,
                        title=title,
                        content=content,
                        history_content=best_content,
                        history_result=best_result,
                    )
                normal = level.get_word_count(grade).normal
                result = svc.compare_handler(res, best_result, normal, content_length)
                result.review_id = uid

                langfuse_context.update_current_observation(output=result.model_dump())

                await repo.update_article_result(uid=uid, result=result.model_dump())

                # 将新文章和结果插入到Milvus中
                async with MilvusVectorSearch() as milvus:
                    await milvus.insert_article(uid, content, result.model_dump_json())

                await redis_client.rpush(cache_key, uid)  # pyright: ignore[reportGeneralTypeIssues]
                await redis_client.expire(cache_key, settings.ARTICLE_CACHE_EXPIRE_SECONDS)

                @observe(capture_output=False)
                async def compare_result():
                    yield make_sse_event(result)

                return StreamingResponse(
                    compare_result(),
                    media_type='text/event-stream',
                    headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
                )

    @observe(capture_output=False)
    async def generator(llm_config: LLMConfig):
        # 使用增强型评分器进行评分
        async with llms.get_llm(llm_config) as llm:
            state = InputState(
                uid=uid,
                topic=topic,
                title=title,
                content=content,
                content_length=content_length,
                grade=grade,
                level=level,
                llm_config=llm_config,
                llm=llm,
            )

            async for chunk in ReviewWorkflow().execute(state):
                # 区分进度消息和最终结果
                if isinstance(chunk, Progress):
                    # 进度消息不需要符合ReviewOut结构
                    yield make_sse_event(chunk)
                else:
                    # 只有最终结果才转换为ReviewOut
                    result = evaluator.format_review_result(chunk, level, uid)

                    # 最后更新数据库
                    await repo.update_article_result(uid=uid, result=result.model_dump())
                    langfuse_context.update_current_observation(output=result.model_dump())

                    # 将新文章和结果插入到Milvus中
                    async with MilvusVectorSearch() as milvus:
                        await milvus.insert_article(uid, content, result.model_dump_json())

                    await redis_client.rpush(cache_key, uid)  # pyright: ignore[reportGeneralTypeIssues]
                    await redis_client.expire(cache_key, settings.ARTICLE_CACHE_EXPIRE_SECONDS)

                    yield make_sse_event(result)

    return StreamingResponse(
        generator(llm_config),
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )


@router.get('/improve', summary='作文优化v2')
@observe(capture_output=False)
async def improve_v2(
    header: HeaderDep,
    review_id: uuid.UUID = Query(..., description='批改接口所返回的对应UUID'),
    db: AsyncSession = Depends(get_db),
):
    """
    增强型作文修改

    使用ReAct方式基于模型已给出的批改意见对文章进行分析并优化
    四个步骤：
    1. 分析改进点
    2. 生成优化内容（实时流式返回）
    3. 预估优化后的分数
    4. 生成优化总结（实时流式返回）
    """

    repo = ArticleRepository(db)

    ai_configs = {
        'analysis': await repo.get_ai_config_by_module('article:enhance-improve_analysis'),
        'improve': await repo.get_ai_config_by_module('article:enhance-improve_content'),
        'summary': await repo.get_ai_config_by_module('article:enhance-improve_summary_v2'),
        'eval': await repo.get_ai_config_by_module('article:review:v2'),
    }
    # 校验配置
    if not all(ai_configs.values()):
        missing = [k for k, v in ai_configs.items() if not v]
        raise HTTPException(500, detail=f'未找到对应模块的 AI 配置: {missing}')

    instance = await repo.get_article_by_uid(review_id)
    if instance is None:
        raise HTTPException(status_code=404, detail='该review_id不存在')

    langfuse_context.update_current_trace(session_id=instance.uid)

    # 获取作文级别和年级信息
    level = Level(instance.level)
    grade = Grade(instance.extra.get('grade')) if instance.extra and 'grade' in instance.extra else None

    # 提取标题
    topic_list = instance.topic.split('\n')
    topic, title = topic_list[0], topic_list[-1]

    if level == Level.XIAOXUE and grade is None:
        raise HTTPException(status_code=400, detail='小学作文优化必须提供年级信息')

    async def process_improvement(ai_configs: dict[str, LLMConfig]):
        # 准备数据
        original_content = instance.content
        # 去除作文中间出现的空格,避免影响标点符号扣分步骤评分
        new_content = remove_blank_characters(original_content)
        new_content = ArticleService.convert_english_punctuation_to_chinese(new_content)

        llm_config_analysis = ai_configs['analysis']

        # 1. 分析改进点
        async with get_llm(llm_config_analysis) as llm:
            analysis_data = await improver.analyze_improvement(
                llm=llm,
                model=llm_config_analysis.model,
                temperature=llm_config_analysis.params.temperature,
                topic=topic,
                title=title,
                content=original_content,
                ref_data=instance.result,  # pyright: ignore[reportArgumentType]
            )

        # 2. 直接生成优化内容
        word_count = level.get_word_count(grade)
        content_length = count_effective_chars(new_content)
        level_score = level.get_score()

        # 生成内容提示词
        prompt = await PromptTemplates.improve_content_prompt(
            topic,
            title,
            original_content,
            instance.result,
            analysis_data,
            content_length,
            word_count,
            level_score,
        )

        llm_config_improve = ai_configs['improve']
        async with get_llm(llm_config_improve) as llm:
            improved_content = await improver.improve_with_length_check(
                llm=llm,
                model=llm_config_improve.model,
                system_prompt=prompt,
                temperature=llm_config_improve.params.temperature,
                min_length=word_count.normal,
                max_length=word_count.max,
            )

        # 3. 生成优化总结
        # 生成总结提示词
        summary_prompt = await PromptTemplates.improve_summary_prompt(
            original_content, improved_content, title, analysis_data
        )

        llm_config_summary = ai_configs['summary']
        summary_params = llms.get_params(
            llm_config=llm_config_summary, messages=[{'role': 'system', 'content': summary_prompt}]
        )

        async with get_llm(llm_config_summary) as llm:
            try:
                summary = await llms.llm_chat(llm, summary_params)
            except Exception as e:
                await logger.aerror(str(e))
                summary = ''  # 提供默认值

        uid = str(uuid.uuid4())  # 确保生成一个新的UUID

        # 保存
        await repo.create_article_improved(
            review_id=str(instance.uid),
            source_content=str(instance.content),
            content=improved_content,
            summary=summary,
            uid=uid,
        )

        # 优化内容评分异步任务
        cache_key = ArticleService.get_cache_key(header, level.value)
        # 去除作文中间出现的空格,避免影响标点符号扣分步骤评分
        new_content = remove_blank_characters(improved_content)
        new_content = ArticleService().convert_english_punctuation_to_chinese(new_content)

        asyncio.create_task(
            ImproverTask().improved_content_evaluate(
                llm_config=ai_configs['eval'],
                uid=uid,
                cache_key=cache_key,
                topic=topic,
                content=improved_content,
                content_length=len(new_content),
                level=level,
                grade=grade,
            )
        )

        # 保存结果供trace_fuse使用
        final_result = ImprovedProcessResult(uid=uid, improved_content=improved_content, summary=summary)
        langfuse_context.update_current_observation(output=final_result)

        return final_result

    # 调用处理函数并返回结果
    result = await process_improvement(ai_configs)  # pyright: ignore[reportArgumentType]
    return ApiResponse(data=result)


@router.post('/retry_improved_review', summary='优化作文批改重试')
@observe(capture_output=False)
async def improved_review(
    header: HeaderDep,
    uid: uuid.UUID = Query(..., description='优化记录UUID'),
    db: AsyncSession = Depends(get_db),
):
    repo = ArticleRepository(db)

    # 获取优化作文记录
    improved_instance = await repo.get_article_improved_by_uid(uid)
    if improved_instance is None:
        raise HTTPException(status_code=404, detail='该优化作文uid不存在')

    # 从数据库获取作文
    article_instance = await repo.get_article_by_uid(improved_instance.a_uid)
    if article_instance is None:
        raise HTTPException(status_code=404, detail='原作文不存在')

    if improved_instance.result_task_status == ImprovedStatus.SUCCESS or improved_instance.result:
        raise HTTPException(status_code=400, detail='该优化作文已评分，无需重新评分')

    if improved_instance.result_task_status == ImprovedStatus.PENDING and improved_instance.updated_at > (
        datetime.now() - timedelta(hours=1)
    ):
        raise HTTPException(status_code=400, detail='该优化作文评分进行中，请耐心等待结果')

    # 获取AI模型实例数据
    llm_config = await repo.get_ai_config_by_module('article:review:v2')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    uid_str = str(uid)
    await repo.update_article_improved_result(
        uid=uid_str,
        status=ImprovedStatus.PENDING.value,
        result={},
    )

    # 去除作文中间出现的空格,避免影响标点符号扣分步骤评分
    new_content = remove_blank_characters(improved_instance.content)
    new_content = ArticleService().convert_english_punctuation_to_chinese(new_content)

    grade = None
    level = Level(article_instance.level)
    if level == Level.XIAOXUE and article_instance.extra:
        grade = article_instance.extra.get('grade')
    cache_key = ArticleService.get_cache_key(header, level.value)

    asyncio.create_task(
        ImproverTask().improved_content_evaluate(
            llm_config=llm_config,
            uid=uid_str,
            cache_key=cache_key,
            topic=article_instance.topic,
            content=improved_instance.content,
            content_length=len(new_content),
            level=Level(article_instance.level),
            grade=grade,
        )
    )

    return ApiResponse(data={})


@router.get('/get_improved_result', summary='获取优化作文评分结果')
@observe(capture_output=False)
async def get_improved_result(
    uid: uuid.UUID = Query(..., description='优化记录UUID'),
    db: AsyncSession = Depends(get_db),
):
    repo = ArticleRepository(db)

    # 获取优化作文记录
    instance = await repo.get_article_improved_by_uid(uid)
    if instance is None:
        raise HTTPException(status_code=404, detail='该优化作文uid不存在')

    if instance.result:
        # 评分完成
        score = instance.result.get('total_score', 0)
        response_data = ImprovedResultOut(status=instance.result_task_status, score=score)
        return ApiResponse(data=response_data)

    # 评分未完成
    if instance.result_task_status == ImprovedStatus.SUCCESS.value:
        raise HTTPException(status_code=500, detail='评分异常，评分结果不存在')

    if instance.result_task_status == ImprovedStatus.PENDING.value and instance.updated_at < (
        datetime.now() - timedelta(hours=1)
    ):
        instance.result_task_status = ImprovedStatus.TIMEOUT.value  # 执行超时
        # 状态更新为执行超时
        await repo.update_article_improved_result(
            uid=str(uid),
            status=ImprovedStatus.TIMEOUT.value,
            result={},
        )

    response_data = ImprovedResultOut(status=instance.result_task_status, score=0)
    return ApiResponse(data=response_data)
