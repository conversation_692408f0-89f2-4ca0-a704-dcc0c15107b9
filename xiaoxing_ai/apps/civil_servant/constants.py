"""
公务员考试评价常量模块
"""

from enum import StrEnum


class ExamQuestionType(StrEnum):
    """面试问题类型"""

    ANALYSIS = 'analysis'  # 分析
    SIMULATION = 'simulation'  # 模拟

    @property
    def eval_key(self):
        """评价提示词 key"""
        return f'interview_evaluation_{self.value}'

    @property
    def improved_key(self):
        """优化提示词 key"""
        return f'interview_improved_{self.value}'

    @property
    def standard_key(self):
        """标准答案提示词 key"""
        return f'interview_standard_{self.value}'


class ImprovedResultType(StrEnum):
    """优化结果类型"""

    RESULT = 'result'
    EVALUATE = 'evaluate'
