"""
公务员考试评价DTO模型
定义API请求和响应的数据模型
"""

from pydantic import BaseModel, Field

from ..constants import ExamQuestionType, ImprovedResultType


class InterviewBase(BaseModel):
    question: str = Field(..., description='面试题目')
    type: ExamQuestionType = Field(
        default=ExamQuestionType.ANALYSIS,
        description='题目类型, simulation:模拟; analysis:现象分析;',
    )
    answer: str = Field(..., description='考生回答')


class InterviewEvaluationIn(InterviewBase): ...


class InterviewEvaluationOut(BaseModel):
    uuid: str = Field(..., description='uuid')
    content: str = Field(..., description='内容')


class InterviewImprovedIn(InterviewBase):
    evaluation: str | None = Field(default=None, description='回答评价')


class InterviewImprovedOut(InterviewEvaluationOut):
    type: ImprovedResultType = Field(..., description='类型, result: 优化后的结果; evaluate: 优化后结果的评价')


class InterviewAiAnswerIn(BaseModel):
    question: str = Field(..., description='面试题目')
    type: ExamQuestionType = Field(
        default=ExamQuestionType.ANALYSIS,
        description='题目类型, simulation:模拟; analysis:现象分析;',
    )


class InterviewAiAnswerOut(InterviewImprovedOut): ...
