"""
公务员考试评价存储库模块
提供数据库操作接口
"""

from typing import Optional

from sqlalchemy import Update, select

from xiaoxing_ai.db.models import InterviewImprovedRecords, InterviewQuestion, InterviewRecords
from xiaoxing_ai.db.repository import CommonRepository


class InterviewRepository(CommonRepository):
    """面试评价存储库"""

    app_name = 'civil_servant'

    async def create_interview_record(self, uid: str, question: str, answer: str, typ: str) -> InterviewRecords:
        obj = InterviewRecords(uid=uid, question=question, answer=answer, type=typ)
        self.db.add(obj)
        await self.db.commit()
        return obj

    async def update_interview_evaluation(self, uid: str, evaluation: str) -> bool:
        """更新面试记录的评价结果"""
        stmt = Update(InterviewRecords).where(InterviewRecords.uid == uid).values(evaluation=evaluation)
        result = await self.db.execute(stmt)

        await self.db.commit()
        if result.rowcount == 0:
            return False
        return True

    async def create_standard_question(
        self,
        uid: str,
        question: str,
        typ: str,
        ai_answer: str,
        ai_evaluation: str,
    ) -> InterviewQuestion:
        obj = InterviewQuestion(
            uid=uid,
            question=question,
            type=typ,
            standard_answer=ai_answer,
            evaluation=ai_evaluation,
        )
        self.db.add(obj)
        await self.db.commit()
        return obj

    async def get_interview_question_by_question(self, question: str, typ: str) -> Optional[InterviewQuestion]:
        """根据问题,类型获取标准答案"""
        stmt = select(InterviewQuestion).where(
            InterviewQuestion.question == question,
            InterviewQuestion.type == typ,
        )
        q = await self.db.execute(stmt)
        return q.scalars().first()

    async def create_improved_record(
        self,
        uid: str,
        question: str,
        answer: str,
        evaluation: str,
        improved_answer: str,
        improved_evaluation: str,
    ) -> InterviewImprovedRecords:
        obj = InterviewImprovedRecords(
            uid=uid,
            question=question,
            answer=answer,
            evaluation=evaluation,
            improved_answer=improved_answer,
            improved_evaluation=improved_evaluation,
        )
        self.db.add(obj)
        await self.db.commit()
        return obj
