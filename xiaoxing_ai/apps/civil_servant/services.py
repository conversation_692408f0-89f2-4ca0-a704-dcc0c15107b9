import structlog
from fastapi import HTTPException
from openai.types.chat import ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam

from xiaoxing_ai.shared.llms import get_params

from .constants import ExamQuestionType
from .dto.models import InterviewAiAnswerIn, InterviewImprovedIn
from .repository import InterviewRepository

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class InterviewService:
    def __init__(self, repo: InterviewRepository):
        self.repo = repo

    async def _build_llm_params(self, prompt_key: str, module: str) -> tuple:
        template = await self.repo.get_model_prompt(prompt_key)
        if not template:
            logger.error(f'不存在 {prompt_key} 提示词模板')
            raise HTTPException(status_code=500, detail='提示词模板缺失')

        llm_config = await self.repo.get_ai_config_by_module(module)
        if not llm_config:
            raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

        return llm_config, template

    async def get_evaluate_llm_params(self, typ: ExamQuestionType, question: str, answer: str):
        llm_config, template = await self._build_llm_params(typ.eval_key, 'civil_servant:interview:evaluate')

        prompt = template.render(question=question, student_answer=answer)

        # 使用GEMINI模型评价考生回答
        params = get_params(
            llm_config,
            [
                ChatCompletionUserMessageParam(content=prompt, role='user'),
            ],
        )

        return llm_config, params

    async def get_improve_llm_params(self, data: InterviewImprovedIn, standard_answer: str):
        llm_config, template = await self._build_llm_params(data.type.improved_key, 'civil_servant:interview:improved')

        prompt = template.render(
            question=data.question,
            answer=data.answer,
            evaluation=data.evaluation or '',
            standard_answer=standard_answer,
        )

        # 使用GEMINI模型进行答案改进
        params = get_params(
            llm_config,
            [
                ChatCompletionUserMessageParam(content=prompt, role='user'),
            ],
        )

        return llm_config, params

    async def get_ai_answer_llm_params(self, data: InterviewAiAnswerIn):
        llm_config, template = await self._build_llm_params(data.type.standard_key, 'civil_servant:interview:ai_answer')

        prompt = template.render(question=data.question)

        params = get_params(
            llm_config,
            [ChatCompletionSystemMessageParam(content=prompt, role='system')],
        )

        return llm_config, params
