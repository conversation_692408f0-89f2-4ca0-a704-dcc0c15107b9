import uuid

import structlog
from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from langfuse.decorators import langfuse_context, observe
from sqlalchemy.ext.asyncio import AsyncSession

from xiaoxing_ai.db import get_db
from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.helper import calculate_similarity
from xiaoxing_ai.shared.sse import make_sse_event

from .constants import ImprovedResultType
from .dto.models import (
    InterviewAiAnswerIn,
    InterviewAiAnswerOut,
    InterviewEvaluationIn,
    InterviewEvaluationOut,
    InterviewImprovedIn,
    InterviewImprovedOut,
)
from .repository import InterviewRepository
from .services import InterviewService

logger = structlog.stdlib.get_logger('xiaoxing_ai')

router = APIRouter(prefix='/civil-servant/interview', tags=['civil-servant'])


@router.post('/evaluation', summary='评价答案', response_model=InterviewEvaluationOut)
@observe(capture_output=False)
async def interview_evaluation(
    data: InterviewEvaluationIn,
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    """返回流式数据"""

    uid = str(uuid.uuid4())

    repo = InterviewRepository(db)
    # 直接入库
    await repo.create_interview_record(uid=uid, question=data.question, answer=data.answer, typ=data.type)

    # 判断答案相似度, 相似则直接返回标准答案
    instance = await repo.get_interview_question_by_question(data.question, data.type)
    if instance:
        similarity = calculate_similarity(data.answer, instance.standard_answer)
        # 如果相似度超过阈值，直接使用题库中的评价和标准答案
        if similarity > 0.7:  # 70%相似度阈值

            async def stream_cached_result():
                for chunk in str(instance.evaluation):
                    out = InterviewEvaluationOut(uuid=uid, content=chunk)
                    yield make_sse_event(out)

            # 保存
            await repo.update_interview_evaluation(uid, instance.evaluation)

            return StreamingResponse(
                stream_cached_result(),
                media_type='text/event-stream',
                headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
            )

    langfuse_context.update_current_trace(session_id=uid)

    llm_config, params = await InterviewService(repo).get_evaluate_llm_params(data.type, data.question, data.answer)

    @observe(capture_output=False)
    async def generator():
        buffer = []
        try:
            async with llms.get_llm(llm_config) as llm:
                res = await llms.llm_chat_stream(llm, params)
                async for chunk in res:
                    chunk_content = chunk.choices[0].delta.content or ''
                    if not chunk_content:
                        continue

                    buffer.append(chunk_content)

                    out = InterviewEvaluationOut(uuid=uid, content=chunk_content)
                    yield make_sse_event(out)

        except Exception as e:
            await logger.aexception('生成评价内容失败', exc_info=e)
            out = InterviewEvaluationOut(uuid=uid, content='生成评价内容失败!')
            yield make_sse_event(out)

        evaluation = ''.join(buffer)
        await repo.update_interview_evaluation(uid, evaluation)

    return StreamingResponse(
        generator(),
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )


@router.post('/improved', summary='优化答案', response_model=InterviewImprovedOut)
@observe(capture_output=False)
async def interview_improved(
    data: InterviewImprovedIn,
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    """返回流式数据"""

    uid = str(uuid.uuid4())
    repo = InterviewRepository(db)
    langfuse_context.update_current_trace(session_id=uid)

    # 获取标准答案
    standard = await repo.get_interview_question_by_question(data.question, data.type)
    if standard:
        standard_answer = standard.standard_answer
    else:
        standard_answer = ''

    svc = InterviewService(repo)
    llm_config, params = await svc.get_improve_llm_params(data, standard_answer)

    @observe(capture_output=False)
    async def generator():
        result_buffer = []
        try:
            async with llms.get_llm(llm_config) as llm:
                res = await llms.llm_chat_stream(llm, params)
                async for chunk in res:
                    chunk_content = chunk.choices[0].delta.content or ''
                    if not chunk_content:
                        continue

                    result_buffer.append(chunk_content)

                    out = InterviewImprovedOut(uuid=uid, type=ImprovedResultType.RESULT, content=chunk_content)
                    yield make_sse_event(out)

        except Exception as e:
            await logger.aexception('生成优化答案内容失败', exc_info=e)
            out = InterviewImprovedOut(uuid=uid, type=ImprovedResultType.RESULT, content='生成优化答案内容失败!')
            yield make_sse_event(out)

        improved_answer = ''.join(result_buffer)

        eval_llm_config, eval_params = await svc.get_evaluate_llm_params(data.type, data.question, improved_answer)
        eval_buffer = []
        try:
            async with llms.get_llm(eval_llm_config) as llm:
                evaluate_res = await llms.llm_chat_stream(llm, eval_params)
                async for chunk in evaluate_res:
                    chunk_content = chunk.choices[0].delta.content or ''
                    if not chunk_content:
                        continue

                    eval_buffer.append(chunk_content)

                    out = InterviewImprovedOut(uuid=uid, type=ImprovedResultType.EVALUATE, content=chunk_content)
                    yield make_sse_event(out)

        except Exception as e:
            await logger.aexception('生成优化答案评价内容失败', exc_info=e)
            out = InterviewImprovedOut(uuid=uid, type=ImprovedResultType.RESULT, content='生成优化答案评价内容失败!')
            yield make_sse_event(out)

        improved_evaluation = ''.join(eval_buffer)

        # 存入优化记录表
        await repo.create_improved_record(
            uid=uid,
            question=data.question,
            answer=data.answer,
            evaluation=data.evaluation or '',
            improved_answer=improved_answer,
            improved_evaluation=improved_evaluation,
        )

    return StreamingResponse(
        generator(),
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )


@router.post('/ai_answer', summary='ai回答', response_model=InterviewAiAnswerOut)
@observe(capture_output=False)
async def interview_ai_answer(
    data: InterviewAiAnswerIn,
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    """返回流式数据"""
    uid = str(uuid.uuid4())
    langfuse_context.update_current_trace(session_id=uid)

    repo = InterviewRepository(db)
    # 判断问题是否存在, 存在返回, 不存在生成并入库
    instance = await repo.get_interview_question_by_question(data.question, data.type)

    if instance:

        @observe(capture_output=False)
        async def cache_result():
            for chunk in str(instance.standard_answer):
                out = InterviewAiAnswerOut(uuid=str(instance.uid), type=ImprovedResultType.RESULT, content=chunk)
                yield make_sse_event(out)

            for chunk in str(instance.evaluation):
                out = InterviewAiAnswerOut(uuid=str(instance.uid), type=ImprovedResultType.EVALUATE, content=chunk)
                yield make_sse_event(out)

        return StreamingResponse(
            cache_result(),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )

    svc = InterviewService(repo)
    llm_config, params = await svc.get_ai_answer_llm_params(data)

    @observe(capture_output=False)
    async def generator():
        ai_answer_buffer = []
        try:
            async with llms.get_llm(llm_config) as llm:
                res = await llms.llm_chat_stream(llm, params)
                async for chunk in res:
                    chunk_content = chunk.choices[0].delta.content or ''
                    if not chunk_content:
                        continue

                    ai_answer_buffer.append(chunk_content)

                    out = InterviewAiAnswerOut(uuid=uid, type=ImprovedResultType.RESULT, content=chunk_content)
                    yield make_sse_event(out)

        except Exception as e:
            await logger.aexception('生成ai答题内容失败', exc_info=e)
            out = InterviewAiAnswerOut(uuid=uid, type=ImprovedResultType.RESULT, content='生成ai答题内容失败!')
            yield make_sse_event(out)

        ai_answer = ''.join(ai_answer_buffer)

        eval_llm_config, eval_params = await svc.get_evaluate_llm_params(data.type, data.question, ai_answer)
        ai_eval_buffer = []
        try:
            async with llms.get_llm(eval_llm_config) as llm:
                ai_evaluate_res = await llms.llm_chat_stream(llm, eval_params)
                async for chunk in ai_evaluate_res:
                    chunk_content = chunk.choices[0].delta.content or ''
                    if not chunk_content:
                        continue

                    ai_eval_buffer.append(chunk_content)

                    out = InterviewAiAnswerOut(uuid=uid, type=ImprovedResultType.EVALUATE, content=chunk_content)
                    yield make_sse_event(out)

        except Exception as e:
            await logger.aexception('生成ai答题结果优化内容失败', exc_info=e)
            out = InterviewAiAnswerOut(
                uuid=uid, type=ImprovedResultType.EVALUATE, content='生成ai答题结果优化内容失败!'
            )
            yield make_sse_event(out)

        ai_evaluation = ''.join(ai_eval_buffer)

        await repo.create_standard_question(
            uid=uid,
            question=data.question,
            typ=data.type,
            ai_answer=ai_answer,
            ai_evaluation=ai_evaluation,
        )

    return StreamingResponse(
        generator(),
        media_type='text/event-stream',
        headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
    )
