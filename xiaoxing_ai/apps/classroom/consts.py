from enum import IntEnum, StrEnum, auto


class TextToolType(IntEnum):
    POLISH = auto()  # 润色
    CONTINUE = auto()  # 续写
    QA = auto()  # 问答
    MODIFY = auto()  # 改写
    REWRITE = auto()  # 重写
    REFINE = auto()  # 缩写

    @property
    def prompt_key(self) -> str:
        """生成prompt key"""
        return f'text_tool_{self.name.lower()}'


class TextToolFormatType(StrEnum):
    TEXT = 'text'
    MARKDOWN = 'markdown'


class LectureNotesType(IntEnum):
    CREATE = auto()  # 生成
    POLISH = auto()  # 润色
    CONTINUE = auto()  # 续写
    REFINE = auto()  # 缩写
    SPEECHIFY = auto()  # 口语化转写

    @property
    def prompt_key(self) -> str:
        """生成数据库中的name字段值"""
        return f'lecture_notes_{self.name.lower()}'
