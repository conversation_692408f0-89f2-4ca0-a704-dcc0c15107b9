import uuid

from pydantic import BaseModel, Field

from xiaoxing_ai.apps.classroom.consts import LectureNotesType, TextToolFormatType, TextToolType
from xiaoxing_ai.consts import ChatEventType


class ChatIn(BaseModel):
    """问答输入模型"""

    session_id: uuid.UUID | None = Field(default=None, description='会话ID，如果为空则创建新会话')
    query: str = Field(..., description='用户问题')


class ChatMessage(BaseModel):
    """聊天会话"""

    session_id: uuid.UUID | str = Field(..., description='会话ID')
    content: str = Field(..., description='消息内容')
    type: ChatEventType = Field(
        default=ChatEventType.COMPLETION,
        description='需要处理的事件:"completion"-默认拼接,"clear"-清除之前的内容',
    )


class TextToolIn(BaseModel):
    input: str = Field(..., description='输入文本')
    type: TextToolType = Field(
        TextToolType.POLISH, description='操作类型 1: 润色; 2: 续写; 3: 问答; 4: 改写; 5: 重写; 6: 缩写;'
    )
    format: TextToolFormatType = Field(TextToolFormatType.TEXT, description='输出文本格式; text 或 markdown')
    command: str | None = Field(None, alias='command', description='要求，例如字数限制、风格要求等', max_length=1000)


class TextToolOut(BaseModel):
    content: str = Field(..., description='消息内容')


class LectureNotesIn(BaseModel):
    input: str = Field(..., description='输入文本', max_length=20000)
    type: LectureNotesType = Field(
        LectureNotesType.CREATE, description='操作类型 1:生成; 2: 润色; 3: 续写; 4: 缩写; 5: 口语化转写'
    )
    command: str | None = Field(None, alias='command', description='要求，例如字数限制、风格要求等', max_length=1000)


class QuestionAiAnswerIn(BaseModel):
    title: str = Field(..., description='问答标题')
