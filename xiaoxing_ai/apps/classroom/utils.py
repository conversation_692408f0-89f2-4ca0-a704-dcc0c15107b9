from langfuse.decorators import observe
from pydantic import BaseModel, Field

from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.llms import LLMConfig, llm_chat_stream
from xiaoxing_ai.shared.sse import make_sse_event


class ContentModel(BaseModel):
    content: str = Field(..., description='消息内容')


@observe(as_type='generation')
async def llm_generator(llm_config: LLMConfig, params: dict):
    """流式生成 LLM 响应，格式化为 SSE"""
    async with llms.get_llm(llm_config) as llm:
        async for chunk in await llm_chat_stream(llm, params):
            try:
                if content := chunk.choices[0].delta.content:  # 提取有效内容
                    out = ContentModel(content=content)

                    yield make_sse_event(out)
            except (AttributeError, IndexError):
                continue  # 跳过无效 chunk
