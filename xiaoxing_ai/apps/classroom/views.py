from datetime import datetime

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse
from langfuse.decorators import langfuse_context, observe
from openai.types.chat import ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam
from sqlalchemy.ext.asyncio import AsyncSession

from xiaoxing_ai.consts import ChatEventType
from xiaoxing_ai.db import get_db
from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.api import APIException, ApiResponse
from xiaoxing_ai.shared.llms import llm_chat
from xiaoxing_ai.shared.sse import make_sse_event
from xiaoxing_ai.shared.xfyun import XFAuditService

from .dto import ChatIn, ChatMessage, LectureNotesIn, QuestionAiAnswerIn, TextToolIn, TextToolOut
from .repository import ClassroomRepository
from .utils import llm_generator

logger = structlog.stdlib.get_logger('xiaoxing_ai')
router = APIRouter(prefix='/classroom', tags=['晓星AI课堂'])


@router.post('/chat', summary='问答聊天（基于网络搜索）')
@observe(capture_input=False)
async def chat_with_search(
    data: ChatIn,
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    """知识问答接口(流式返回)"""
    # 处理流程:
    # 1. 检查用户问题是否为空
    # 2. 获取或创建会话:
    #    - 如果提供了session_id且存在，使用现有会话
    #    - 否则创建新会话
    # 3. 获取会话历史记录
    # 4. 调用LLM生成回答
    # 5. 更新会话历史
    # 6. 流式返回结果

    repo = ClassroomRepository(db)

    if not data.query.strip():
        raise HTTPException(status_code=400, detail='问题不能为空')

    session_id = await repo.get_or_create_session(data.session_id)
    if not session_id:
        raise HTTPException(status_code=404, detail=f'会话{data.session_id}不存在')
    langfuse_context.update_current_trace(session_id=session_id)

    harmful, audit_description = await XFAuditService().audit(data.query)

    prompt = await repo.get_model_prompt('chat')
    if not prompt:
        raise APIException(code=500, detail='提示词未配置')

    prompt = prompt.render(
        datetime=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        harmful=harmful,
        audit_description=audit_description,
    )

    history = await repo.get_chat_history(session_id=session_id, n=8)

    context = [dict(role='system', content=prompt)]
    for msg in history:
        context.append(dict(role='user', content=msg.input))
        context.append(dict(role='assistant', content=msg.output))

    # 获取AI模型实例数据
    llm_config = await repo.get_ai_config_by_module('classroom:chat')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    params = dict(
        model=llm_config.model,
        messages=[*context, dict(role='user', content=data.query)],
        temperature=llm_config.params.temperature,
        stream=True,
        tools=llm_config.params.tools,
    )

    @observe(as_type='generation', capture_output=False)
    async def generator(llm_config, params):
        async with llms.get_llm(llm_config) as llm:
            r = await llm.chat.completions.create(**params)  # type: ignore
            buffer = []
            async for chunk in r:
                if content := chunk.choices[0].delta.content:
                    buffer.append(content)

                msg = ChatMessage(session_id=session_id, content=content)

                # 智谱方面的内容审查关键词： https://bigmodel.cn/dev/howuse/securityaudit
                if chunk.choices[0].finish_reason == 'sensitive':
                    content = '抱歉，不如我们再换个话题聊聊吧~'
                    msg.content = content
                    msg.type = ChatEventType.CLEAR

                yield make_sse_event(msg)

                # 当检测到是最后一个chunk时，保存消息
                if chunk.choices[0].finish_reason == 'stop':
                    await repo.create_message(session_id=session_id, input=data.query, content=''.join(buffer))

        langfuse_context.update_current_observation(output=''.join(buffer))

    try:
        return StreamingResponse(
            generator(llm_config, params),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )
    except Exception as e:
        await logger.aexception('Failed to generate response', exc_info=e)
        raise HTTPException(status_code=500, detail='Failed to generate response')


@router.post('/text_tool', summary='ai 文本工具')
@observe(capture_input=False)
async def text_tool(
    data: TextToolIn,
    stream: bool = Query(default=False, description='是否以流式返回结果'),
    db: AsyncSession = Depends(get_db),
):
    repo = ClassroomRepository(db)
    prompt = await repo.get_model_prompt(data.type.prompt_key)
    if not prompt:
        raise APIException(code=500, detail='提示词未配置')

    prompt = prompt.render(
        command=data.command,
        input=data.input,
        format=data.format,
    )

    # 获取AI模型实例数据
    llm_config = await repo.get_ai_config_by_module('classroom:text_tool')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    params = {
        'model': llm_config.model,
        'messages': [
            ChatCompletionUserMessageParam(role='user', content=prompt),
        ],
        'temperature': llm_config.params.temperature,
        'stream': stream,
    }

    try:
        if stream:
            return StreamingResponse(
                llm_generator(llm_config, params),
                media_type='text/event-stream',
                headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
            )

        async with llms.get_llm(llm_config) as llm:
            content = await llm_chat(llm, params)
        return ApiResponse(data={'content': content})

    except Exception as e:
        await logger.aexception('text_tool 生成失败', exc_info=e)
        raise HTTPException(status_code=500, detail='improve error')


@router.post('/lecture_notes', summary='ai 课堂讲稿', response_model=TextToolOut)
@observe(capture_input=False)
async def lecture_notes(data: LectureNotesIn, db: AsyncSession = Depends(get_db)) -> StreamingResponse:
    repo = ClassroomRepository(db)
    prompt = await repo.get_model_prompt(data.type.prompt_key)
    if not prompt:
        raise APIException(code=500, detail='提示词未配置')

    prompt = prompt.render(command=data.command)

    llm_config = await repo.get_ai_config_by_module('classroom:lecture_notes')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    params = {
        'model': llm_config.model,
        'messages': [
            ChatCompletionSystemMessageParam(role='system', content=prompt),
            ChatCompletionUserMessageParam(role='user', content=data.input),
        ],
        'temperature': llm_config.params.temperature,
        'stream': True,
    }

    try:
        return StreamingResponse(
            llm_generator(llm_config, params),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )
    except Exception as e:
        await logger.aexception('lecture_notes 生成失败', exc_info=e)
        raise HTTPException(status_code=500, detail='服务器繁忙, 请重试!')


@router.post('/questions/ai_answer', summary='问答管理-ai回答')
@observe(capture_input=False)
async def questions_ai_answer(
    data: QuestionAiAnswerIn,
    db: AsyncSession = Depends(get_db),
) -> StreamingResponse:
    repo = ClassroomRepository(db)

    llm_config = await repo.get_ai_config_by_module('classroom:questions:ai_answer')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')
    prompt = await repo.get_model_prompt('ai_answer')
    if not prompt:
        raise APIException(code=500, detail='提示词未配置')

    params = {
        'model': llm_config.model,
        'messages': [
            ChatCompletionSystemMessageParam(role='system', content=prompt.render()),
            ChatCompletionUserMessageParam(role='user', content=data.title),
        ],
        'temperature': llm_config.params.temperature,
        'stream': True,
    }

    try:
        return StreamingResponse(
            llm_generator(llm_config, params),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )
    except Exception as e:
        await logger.aexception('ai回答 生成失败', exc_info=e)
        raise HTTPException(status_code=500, detail='服务器繁忙, 请重试!')
