from pydantic import BaseModel, Field


class ContentAuditIn(BaseModel):
    text: str = Field(..., description='需要审查的文本', max_length=5000)


class WordInfo(BaseModel):
    word: str = Field(..., description='敏感词')
    positions: list[int] = Field(..., description='敏感词位置下标信息')


class Category(BaseModel):
    confidence: int = Field(..., ge=0, le=100, description='该分类下建议结果的置信度：取值 0-100')
    category: str = Field(..., description='敏感分类')
    suggest: str = Field(..., description='审核建议结果：pass 通过; block 不合规结果')
    category_description: str = Field(..., description='识别类型')
    word_list: list[str] | None = Field(default=None, description='敏感词列表')
    word_infos: list[WordInfo] | None = Field(default=None, description='敏感词附属信息')


class Detail(BaseModel):
    content: str = Field(..., description='审核文本具体内容')
    category_list: list[Category] = Field(..., description='敏感节点列表')


class Result(BaseModel):
    suggest: str = Field(..., description='审核建议结果：pass 通过; block 不合规结果')
    detail: Detail = Field(..., description='审核结果详情')


class Data(BaseModel):
    result: Result = Field(..., description='内容识别结果')
    request_id: str = Field(..., description='文本合规审核的标识')


class ContentAuditOut(BaseModel):
    code: str = Field(..., description='会话调用成功标记：000000 表示调用成功;其他数字 表示会话调用异常')
    desc: str = Field(..., description='对会话调用是否成功的描述')
    data: Data = Field(..., description='审核结果')
    sid: str = Field(..., description='本次会话唯一id标识，用于排查接口调用问题')
