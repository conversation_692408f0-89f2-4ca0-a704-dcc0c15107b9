from typing import Optional

import structlog
from fastapi import APIRouter, File, Form, UploadFile

from xiaoxing_ai.shared.api import ApiResponse
from xiaoxing_ai.shared.baidu import BaiduOCR, DocumentEnhance, Point
from xiaoxing_ai.shared.xfyun import XFAuditService

from .schemas import ContentAuditIn, ContentAuditOut

logger = structlog.stdlib.get_logger('xiaoxing_ai')

router = APIRouter(prefix='', tags=['common'])


@router.post('/ocr/document/enhance', summary='文档图片矫正')
async def enhance_document(
    file: UploadFile = File(...),
    scan_type: int = Form(
        3,
        description='1-只做检测返回四角点坐标; '
        '2-只做矫正,需传入四角点坐标; '
        '3-默认值,检测并矫正,返回主体在原图中的四角点坐标以及矫正后的图像',
    ),
    points: Optional[str] = Form(None, description='当scan_type=2时需要传入的四角点坐标,格式为x1,y1;x2,y2;x3,y3;x4,y4'),
    enhance_type: int = Form(0, description='是否开启图像增强功能; 0-默认; 1-去阴影; 2-增强并锐化; 3-黑白滤镜'),
) -> ApiResponse[DocumentEnhance]:
    """文档图片进行矫正"""

    # 读取上传的图片数据
    if file.size and file.size > 4 * 1024 * 1024:  # 4MB in bytes
        return ApiResponse(code=400, msg='图片大小不能超过4MB')

    image_data = await file.read()

    # 解析points参数
    point_list = None
    if scan_type == 2 and points:
        try:
            # 解析坐标字符串,格式为x1,y1;x2,y2;x3,y3;x4,y4
            point_pairs = [pair.split(',') for pair in points.split(';')]
            point_list = [Point(x=int(x), y=int(y)) for x, y in point_pairs]
            if len(point_list) != 4:
                raise ValueError('必须提供4个点的坐标')
        except (ValueError, IndexError) as e:
            return ApiResponse(code=400, msg=f'坐标格式错误: {str(e)}')

    # 创建OCR客户端并识别文字
    ocr_client = BaiduOCR()
    data = await ocr_client.enhance_document(
        image_data,
        scan_type=scan_type,
        points=point_list,
        enhance_type=enhance_type,
    )

    return ApiResponse(data=data)


@router.post('/audit/text', summary='文本内容合规审查')
async def content_audit(data: ContentAuditIn) -> ApiResponse[ContentAuditOut]:
    svc = XFAuditService()
    try:
        resp = await svc.text_moderate(data.text)
    except Exception as e:
        await logger.ainfo(f'err: {e}')
        return ApiResponse(code=400, msg='合规审查服务失败')

    return ApiResponse(data=resp)
