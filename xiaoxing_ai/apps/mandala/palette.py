import gc

import cv2
import numpy as np
from sklearn.cluster import MiniBatchKMeans

from .dto import Palette, RGBColor


class ColorScaleAnalyzer:
    def __init__(
        self,
        max_size: int = 512,
        n_clusters: int = 24,
        batch_size: int = 1024,
        max_iter: int = 50,
        n_init: int = 3,
        random_state: int = 42,
        white_threshold: int = 220,
        max_samples: int = 50000,
        min_component_area: float = 0.001,
    ):
        # 图像处理参数
        self.max_size = max_size
        self.white_threshold = white_threshold
        self.min_component_area = min_component_area

        # 聚类算法参数
        self.n_clusters = n_clusters
        self.batch_size = batch_size
        self.max_iter = max_iter
        self.n_init = n_init
        self.random_state = random_state
        self.max_samples = max_samples

    def _resize_image(self, img: np.ndarray) -> np.ndarray:
        """按比例缩放图像，保持长宽比"""
        h, w = img.shape[:2]
        if max(h, w) <= self.max_size:
            return img

        scale = self.max_size / max(h, w)
        return cv2.resize(img, (int(w * scale), int(h * scale)), interpolation=cv2.INTER_AREA)

    def _process_black_lines(self, img_rgb: np.ndarray) -> np.ndarray:
        """黑线检测与修复"""
        # 创建黑白掩膜
        lower_black = np.array([0, 0, 0], dtype=np.uint8)
        upper_black = np.array([40, 40, 40], dtype=np.uint8)
        mask = cv2.inRange(img_rgb, lower_black, upper_black)

        # 形态学开运算去除噪点
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        cleaned = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=2)

        # 连通域分析过滤小区域
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(cleaned)
        final_mask = np.zeros_like(mask)
        total_pixels = img_rgb.shape[0] * img_rgb.shape[1]

        for i in range(1, num_labels):
            if stats[i, cv2.CC_STAT_AREA] > total_pixels * self.min_component_area:
                final_mask[labels == i] = 255

        return cv2.inpaint(img_rgb, final_mask, 3, cv2.INPAINT_TELEA)

    def _load_image(self, img_data: bytes) -> tuple[np.ndarray, np.ndarray | None]:
        """加载并解码图像，处理透明度通道"""
        np_array = np.frombuffer(img_data, dtype=np.uint8)
        img = cv2.imdecode(np_array, flags=cv2.IMREAD_UNCHANGED)

        if img.ndim == 3:
            if img.shape[2] == 4:  # 包含alpha通道
                b, g, r, a = cv2.split(img)
                return cv2.merge([r, g, b]), a
            elif img.shape[2] == 3:  # RGB格式
                return cv2.cvtColor(img, cv2.COLOR_BGR2RGB), None

        raise ValueError(f'不支持的图像格式：通道数 {img.ndim}')

    def _extract_pixels(self, img_rgb: np.ndarray, alpha: np.ndarray | None) -> np.ndarray:
        """提取有效像素数据"""
        h, w = img_rgb.shape[:2]

        if alpha is not None:
            # 调整alpha通道尺寸与处理后的图像一致
            alpha = cv2.resize(alpha, (w, h), interpolation=cv2.INTER_AREA)
            mask = alpha.reshape(-1) > 0
            pixels = img_rgb.reshape(-1, 3)[mask]
        else:
            pixels = img_rgb.reshape(-1, 3)

        return pixels

    def _filter_pixels(self, pixels: np.ndarray) -> np.ndarray:
        """过滤白色像素"""
        if pixels.size == 0:
            return pixels

        white_mask = (
            (pixels[:, 0] >= self.white_threshold)
            & (pixels[:, 1] >= self.white_threshold)
            & (pixels[:, 2] >= self.white_threshold)
        )
        return pixels[~white_mask]

    def _subsample(self, pixels: np.ndarray) -> np.ndarray:
        """像素子采样"""
        if len(pixels) <= self.max_samples:
            return pixels

        indices = np.random.choice(len(pixels), size=self.max_samples, replace=False)
        return pixels[indices]

    def _cluster_colors(self, pixels: np.ndarray) -> MiniBatchKMeans:
        """执行颜色聚类"""
        kmeans = MiniBatchKMeans(
            n_clusters=self.n_clusters,
            batch_size=self.batch_size,
            max_iter=self.max_iter,
            n_init=self.n_init,  # type: ignore str,but int
            random_state=self.random_state,
        )
        kmeans.fit(pixels)
        return kmeans

    @staticmethod
    def _format_results(kmeans: MiniBatchKMeans) -> list[Palette]:
        """格式化聚类结果"""
        total = len(kmeans.labels_)
        results = []

        for idx, center in enumerate(kmeans.cluster_centers_):
            count = np.sum(kmeans.labels_ == idx)
            color = RGBColor(R=int(center[0]), G=int(center[1]), B=int(center[2]))
            palette = Palette(color=color, scale=float(count / total))
            results.append(palette)

        return sorted(results, key=lambda x: x.scale, reverse=True)

    def analyze(self, img_data: bytes) -> list[Palette]:
        """
        完整分析流程：
        1. 加载图像
        2. 预处理（缩放、去黑线）
        3. 提取有效像素
        4. 过滤和采样
        5. 颜色聚类
        6. 生成结果
        """
        try:
            # 1. 加载原始图像
            img_rgb, alpha = self._load_image(img_data)
            # 2. 图像预处理
            img_rgb = self._resize_image(img_rgb)
            img_rgb = self._process_black_lines(img_rgb)
            # 3. 提取有效像素
            pixels = self._extract_pixels(img_rgb, alpha)
            # 4. 像素处理流程
            pixels = self._filter_pixels(pixels)
            if len(pixels) == 0:
                raise ValueError('无有效像素')
            pixels = self._subsample(pixels)
            # 5. 颜色聚类分析
            kmeans = self._cluster_colors(pixels)
            # 6. 生成最终结果
            result = self._format_results(kmeans)
            # 内存清理
            gc.collect()

        except Exception as e:
            gc.collect()
            raise ValueError(f'解析图片出错,err:{e}')

        gc.collect()

        return result
