import structlog
from fastapi import APIRouter, File, UploadFile

from xiaoxing_ai.shared.api import ApiResponse

from .dto import Palette
from .palette import ColorScaleAnalyzer

log = structlog.stdlib.get_logger('xiaoxing_ai.mandala')
router = APIRouter(prefix='/mandala', tags=['mandala'])


@router.post('/palette', summary='获取图片颜色占比信息')
async def get_image_palette(file: UploadFile = File(...)) -> ApiResponse[list[Palette]]:
    """获取图片中的颜色占比情况"""

    # 校验文件是否是图片格式
    supported_mime_types = ['image/jpeg', 'image/png', 'image/bmp', 'image/tiff']
    if file.content_type not in supported_mime_types:
        return ApiResponse(code=400, msg='不支持的文件格式，请上传正确的图片格式')

    if file.size and file.size <= 0:
        return ApiResponse(code=400, msg='文件体积错误，请上传正确的图片文件')

    try:
        content = await file.read()
        palette = ColorScaleAnalyzer().analyze(content)
        return ApiResponse(data=palette)
    except Exception as e:
        await log.aerror(f'解析颜色占比失败,err:{e}')
        return ApiResponse(code=400, msg='解析颜色占比失败')
