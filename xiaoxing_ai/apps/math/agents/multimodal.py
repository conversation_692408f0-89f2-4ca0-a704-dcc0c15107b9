import base64

from langfuse.decorators import observe
from openai.types.chat import (
    ChatCompletionContentPartImageParam,
    ChatCompletionContentPartTextParam,
    ChatCompletionUserMessageParam,
)

from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.helper import extract_json

IMG_TEMPLATE = """
请分析图片中的内容, 判断是否为数学相关的问题
请按照 json 格式进行输出:
```json
{
    "is_math": 是否为数学相关的问题(bool),True/False
}
```
"""


class MathQuestionDetector:
    """视觉理解模型"""

    @observe()
    async def has_math_content(self, image: bytes) -> bool:
        if len(image) == 0:
            return False

        img_base64 = base64.b64encode(image).decode('utf-8')
        params = {
            'model': 'glm-4v-plus-0111',
            'temperature': 0.7,
            'messages': [
                ChatCompletionUserMessageParam(
                    content=[
                        # 暂时去除对图像的分析
                        ChatCompletionContentPartImageParam(
                            image_url={'url': img_base64, 'detail': 'auto'},
                            type='image_url',
                        ),
                        ChatCompletionContentPartTextParam(text=IMG_TEMPLATE, type='text'),
                    ],
                    role='user',
                ),
            ],
            'response_format': {'type': 'json_object'},
        }

        res = await llms.llm_chat(llms.zhipu, params)

        data = extract_json(res)

        return data.get('is_math', False)
