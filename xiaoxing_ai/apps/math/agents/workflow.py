import asyncio
from typing import AsyncGenerator, List, Union

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langchain_openai import AzureChatOpenAI, ChatOpenAI
from pydantic import SecretStr

from xiaoxing_ai.shared.llms import LLMConfig

from ..dto.agent import (
    MathKnowledgeOut,
    MathSolutionOut,
    MathUnderstandingOut,
)


def get_openai_llm(config: LLMConfig) -> Union[AzureChatOpenAI, ChatOpenAI]:
    """获取OpenAI LLM实例

    Args:
        llm_args: 模型实例配置
    """
    if config.provider == 'azure':
        return AzureChatOpenAI(
            api_key=SecretStr(config.api_key),
            model=config.model,
            azure_endpoint=str(config.base_url),
            api_version=config.api_version,
            azure_deployment=config.model,
            temperature=config.params.temperature,
        )
    if config.provider == 'openai':
        # OpenAI
        return ChatOpenAI(
            api_key=SecretStr(config.api_key),
            base_url=str(config.base_url),
            model=config.model,
            temperature=config.params.temperature,
        )

    raise ValueError(f'{config.provider} is not supported.')


async def get_understanding(
    llm_config: LLMConfig, config: RunnableConfig, system_prompt_content: str
) -> MathUnderstandingOut:
    """题意理解智能体节点"""

    # 从config获取注入参数
    messages = build_agent_messages(config, system_prompt_content)
    llm = get_openai_llm(llm_config)
    resp = await llm.ainvoke(messages, response_format=MathUnderstandingOut, config=config)

    out: MathUnderstandingOut = resp.additional_kwargs['parsed']

    return out


async def get_knowledge_point(
    llm_config: LLMConfig, config: RunnableConfig, system_prompt_content: str
) -> MathKnowledgeOut:
    """知识点智能体节点"""
    messages = build_agent_messages(config, system_prompt_content)
    llm = get_openai_llm(llm_config)
    resp = await llm.ainvoke(messages, response_format=MathKnowledgeOut, config=config)

    out: MathKnowledgeOut = resp.additional_kwargs['parsed']

    return out


async def get_solution_steps(
    llm_config: LLMConfig, config: RunnableConfig, system_prompt_content: str
) -> MathSolutionOut:
    """解题步骤智能体节点"""
    # 从config获取注入参数
    messages = build_agent_messages(config, system_prompt_content)
    llm = get_openai_llm(llm_config)
    resp = await llm.ainvoke(messages, response_format=MathSolutionOut, config=config)

    out: MathSolutionOut = resp.additional_kwargs['parsed']

    return out


async def execute(config) -> AsyncGenerator[dict[str, MathKnowledgeOut | MathUnderstandingOut | MathSolutionOut], None]:
    """并发执行多个智能体任务，并以生成器方式逐个返回结果"""
    configurable = config['configurable']
    llm_config = LLMConfig(**configurable.get('llm_config').model_dump())
    prompts = configurable.get('prompts', {})

    # clean configurable
    configurable.pop('prompts')
    configurable.pop('llm_config')
    coroutines = {
        'understanding_agent': get_understanding(llm_config, config, prompts.get('understanding', '')),
        'knowledge_point_agent': get_knowledge_point(llm_config, config, prompts.get('knowledge_points', '')),
        'solution_steps_agent': get_solution_steps(llm_config, config, prompts.get('solution_steps', '')),
    }

    # 创建任务并建立从任务到名称的映射
    tasks_to_names = {asyncio.create_task(coro): name for name, coro in coroutines.items()}
    tasks = set(tasks_to_names.keys())

    while tasks:
        # 等待任一任务完成
        done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)

        for task in done:
            agent_name = tasks_to_names[task]
            result = await task  # 获取结果
            yield {agent_name: result}

        # 更新任务集以继续处理剩余任务
        tasks = pending


def build_agent_messages(
    config: RunnableConfig, system_prompt_content: str
) -> List[Union[SystemMessage, HumanMessage]]:
    """
    为LLM（大型语言模型）调用构建消息列表

    Args:
        config: LangChain的RunnableConfig对象。
        system_prompt_content: prompt内容

    Returns:
        返回消息对象列表.
    """

    _langchain_config = config.get('configurable', {})
    state = _langchain_config.get('state', {})
    problem_content = state.get('current_problem', {}).get('problem', '')

    messages = [SystemMessage(content=system_prompt_content), HumanMessage(content=problem_content)]
    return messages
