import uuid
from datetime import datetime, timedelta, timezone
from typing import Literal

import jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.ext.asyncio import AsyncSession

from xiaoxing_ai.db.jkxt_models import AuthAccount
from xiaoxing_ai.settings import settings
from xiaoxing_ai.shared.api import APIException, ErrorCode

from .repository import YYPXRepository

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')


def verify_password(plain_password: str, hashed_password: str | None) -> bool:
    if not hashed_password:
        return False

    return pwd_context.verify(plain_password, hashed_password)


def create_access_token(
    data: dict,
    token_type: Literal['access', 'refresh'] = 'access',
) -> str:
    """创建token"""
    now = datetime.now(timezone.utc)
    if token_type == 'access':
        expires_date = now + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    else:
        expires_date = now + timedelta(minutes=settings.JWT_REFRESH_TOKEN_EXPIRE_MINUTES)

    to_encode = {
        'lat': int(now.timestamp()),
        'exp': int(expires_date.timestamp()),
        'jti': str(uuid.uuid4()),
        'type': token_type,
        'subject': data,
    }
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, settings.JWT_ALGORITHM)
    return encoded_jwt


def decode_jwt_token(token: str, expected_type: Literal['access', 'refresh'] = 'access'):
    """解析token"""
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
    except jwt.ExpiredSignatureError:
        raise APIException(code=ErrorCode.TOKEN_HAS_EXPIRED, detail='token已过期, 请重新登录')
    except jwt.InvalidTokenError:
        raise APIException(code=ErrorCode.TOKEN_NOT_VALID, detail='无效的token')

    token_type = payload.get('type')
    if token_type != expected_type:
        raise APIException(code=ErrorCode.TOKEN_NOT_VALID, detail='无效的token')

    return payload


async def authenticate_user(db: AsyncSession, account: str, password: str) -> AuthAccount | None:
    """校验token"""
    user = await YYPXRepository(db).get_account_by_account(account)
    if not user:
        return None

    if not verify_password(password, user.password):
        return None

    return user
