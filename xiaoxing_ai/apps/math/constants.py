"""
常量定义
"""

from enum import StrEnum

# 缓存相关常量
CACHE_EXPIRY = 60 * 60 * 24 * 90  # 90天

# 图片相关限制
MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB

# 用户政策/隐私协议等WebView页面共用
NOT_FOUND_PAGE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 Not Found</title>
</head>
<body>
    <h1>404 - Not Found</h1>
    <p>The page you are looking for does not exist.</p>
</body>
</html>
"""

# Datetime format
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


class OCRType(StrEnum):
    SINGLE = 'single-question'
    MULTI = 'multiple-questions'
    PAPER = 'paper'


class LectureType(StrEnum):
    IDEA = 'understanding_agent'  # 解题思路
    KNOWLEDGE = 'knowledge_point_agent'  # 关联知识点
    STEPS = 'solution_steps_agent'  # 解答过程
