from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jwt.exceptions import InvalidTokenError
from sqlalchemy.ext.asyncio import AsyncSession

from xiaoxing_ai.db import get_db, get_yypx_db
from xiaoxing_ai.db.jkxt_models import AuthAccount

from .auth import decode_jwt_token
from .repository import YYPXRepository

auth_scheme = HTTPBearer(scheme_name='JWT')

YYPXSessionDep = Annotated[AsyncSession, Depends(get_yypx_db)]
SessionDep = Annotated[AsyncSession, Depends(get_db)]


async def get_current_user(
    yypx_db: YYPXSessionDep,
    token: HTTPAuthorizationCredentials | None = Depends(auth_scheme),
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail='Could not validate credentials',
        headers={'WWW-Authenticate': 'Bearer'},
    )

    if token is None:
        raise credentials_exception

    credentials = token.credentials
    try:
        payload = decode_jwt_token(credentials)
        # 兼容旧token格式
        user_id = payload.get('id') or payload.get('subject', {}).get('id')
        if user_id is None:
            raise credentials_exception
    except InvalidTokenError:
        raise credentials_exception

    user = await YYPXRepository(yypx_db).get_account_by_id(user_id)
    if user is None:
        raise credentials_exception

    return user


async def get_current_active_user(
    account: AuthAccount = Depends(get_current_user),
):
    if not account.status:
        raise HTTPException(status_code=400, detail='该账号未启用')
    return account


AuthDep = Annotated[AuthAccount, Depends(get_current_active_user)]
