import uuid

from pydantic import BaseModel, Field, computed_field

from xiaoxing_ai.consts import ChatEventType

from ..constants import LectureType
from .question import MathQuestion


class MathLectureIn(BaseModel):
    """数学讲题请求模型"""

    current_problem: MathQuestion = Field(..., description='当前题目信息')
    thread_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description='线程ID')
    record_id: str | None = Field(None, description='搜题记录ID')


class MathQAIn(BaseModel):
    group_id: uuid.UUID = Field(..., description='分组ID')
    session_id: uuid.UUID = Field(..., description='会话ID（可能是子项）')
    query: str = Field(..., description='用户问题')
    type: LectureType = Field(..., description='问题类型')

    @computed_field
    @property
    def is_item(self) -> bool:
        return self.group_id != self.session_id


class MathQAMessage(BaseModel):
    """聊天会话"""

    session_id: uuid.UUID | str = Field(..., description='会话ID')
    content: str = Field(..., description='消息内容')
    type: ChatEventType = Field(
        default=ChatEventType.COMPLETION,
        description='需要处理的事件:"completion"-默认拼接,"clear"-清除之前的内容',
    )


class MathUnderstandingStep(BaseModel):
    """数学题目理解步骤"""

    problem_id: int = Field(..., description='问题编号(必须从1开始)')
    step_id: int = Field(..., description='步骤编号')
    analysis: str = Field(
        ...,
        description='题意分析（分析题目信息，剔除无效信息，'
        '列出已知条件和求解问题，所有数学公式用 `$$` 双美元符号包裹）',
    )
    explanation: str = Field(..., description='思路讲解（目标是如何把思路讲解清晰明白）')
    process: str = Field(
        ...,
        description='计算过程（当前思路的计算过程，'
        '尽量有公式和计算步骤，简洁明了，所有数学公式用 `$$` 双美元符号包裹）',
    )


class MathUnderstandingOut(BaseModel):
    """数学题目理解输出"""

    steps: list[MathUnderstandingStep] = Field(..., description='题意理解步骤列表')


class MathKnowledgePoint(BaseModel):
    """数学知识点模型"""

    point_id: int = Field(..., description='知识点编号')
    name: str = Field(..., description='知识点名称')
    description: str = Field(..., description='知识点详细描述，包含必要的数学公式，所有数学公式用 `$$` 双美元符号包裹')


class MathKnowledgeOut(BaseModel):
    """数学知识点响应"""

    steps: list[MathKnowledgePoint] = Field(..., description='知识点列表')


class MathSolutionStep(BaseModel):
    """数学解题步骤"""

    step_id: int = Field(..., description='步骤编号')
    problem: str = Field(..., description='这一步骤要解决的核心问题，所有数学公式用 `$$` 双美元符号包裹')
    content: str = Field(
        ..., description='步骤内容（详细列出每一个步骤的计算内容，所有数学公式用 `$$` 双美元符号包裹）'
    )
    lecture: str = Field(..., description='计算步骤讲稿（通俗易懂、口语化的文字讲解，避免过多专业术语）')


class MathSolutionOut(BaseModel):
    """数学解题步骤响应"""

    steps: list[MathSolutionStep] = Field(..., description='解题步骤列表')


class MathQAIn2(BaseModel):
    session_id: uuid.UUID = Field(..., description='会话ID（流式讲题返回的record_id）')
    query: str = Field(..., description='用户问题')
    type: LectureType = Field(..., description='问题类型')
