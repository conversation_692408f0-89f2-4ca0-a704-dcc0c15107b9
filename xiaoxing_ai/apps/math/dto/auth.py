from pydantic import BaseModel, Field, SecretStr


class SignupIn(BaseModel):
    username: str = Field(..., description='用户名')
    password: SecretStr = Field(..., description='密码')


class LoginIn(BaseModel):
    account: str = Field(..., alias='username', description='用户名或手机号')
    password: SecretStr = Field(..., description='密码')


class Token(BaseModel):
    access_token: str = Field(..., description='access_token')
    token_type: str = Field(default='bearer', description='token类型')


class LoginOut(Token):
    refresh_token: str = Field(..., description='refresh_token')


class RefreshOut(LoginOut): ...
