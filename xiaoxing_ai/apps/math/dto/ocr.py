from pydantic import BaseModel, Field


class SaveOCRRecordRequest(BaseModel):
    """保存OCR记录请求模型"""

    record_id: str = Field(..., description='OCR记录ID')
    question_type: str = Field(..., description='题目类型：single/multi/paper')
    image_url: str = Field(..., description='图片URL')
    ocr_result: dict = Field(..., description='OCR识别结果，可能经过前端修改')


class OCRRecordOut(BaseModel):
    """OCR记录输出模型"""

    record_id: str = Field(..., description='记录唯一标识')
    question_type: str = Field(..., description='题目类型')
    image_url: str = Field(..., description='题目图片URL')
    cover_image_url: str = Field(..., description='题目封面图片URL，搜多题时取最后题作为封面')
    created_at: str = Field(..., description='创建时间')
    ocr_result: dict = Field(..., description='OCR识别结果')
