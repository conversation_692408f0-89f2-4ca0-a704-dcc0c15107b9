from pydantic import BaseModel, Field


class Metadata(BaseModel):
    js: list = Field(..., description='渲染html所需的js文件地址')
    css: list = Field(..., description='渲染html所需的css文件地址')


class SearchQuestionIn(BaseModel):
    question: str | None = Field(None, description='题目文本内容')
    image_base64: str | None = Field(None, description='题目图片的base64编码，优先使用question参数')
    ocr_record_id: str = Field(..., description='关联的OCR记录ID')
    question_index: int = Field(..., description='题目在OCR中的下标')


class SearchQuestion(BaseModel):
    id: str = Field(..., description='试题id')
    question: str = Field(..., description='试题')
    explanation: str = Field(..., description='解析')
    answer: str = Field(..., description='答案')
    similarity: float = Field(..., description='试题相似度，范围为0~100，值越大越相似')
    metadata: Metadata = Field(..., description='其他元数据,例如js,css文件等')


class SearchQuestionOut(BaseModel):
    record_id: str = Field(..., description='搜题记录id')
    questions: list[SearchQuestion] = Field(..., description='问题列表')


class SearchQuestion2(BaseModel):
    id: str = Field(..., description='试题id')
    question: str = Field(..., description='试题')
    explanation: str = Field(..., description='解析')
    answer: str = Field(..., description='答案')
    similarity: float = Field(..., description='试题相似度，范围为0~100，值越大越相似')


class SearchQuestionOut2(BaseModel):
    record_id: str = Field(..., description='搜题记录id')
    questions: list[SearchQuestion2] = Field(..., description='问题列表')


class MathQuestion(BaseModel):
    """数学讲题请求模型"""

    problem: str = Field(..., description='题目内容')
    grade: str = Field('初三', description='学生年级，例如：小学一年级、初一、高三等')
    question: str | None = Field(None, description='题目HTML，包含可能的公式和图片')
    explanation: str | None = Field(None, description='解析HTML，包含可能的解题步骤和图表')
    answer: str | None = Field(None, description='答案HTML，包含可能的公式和计算过程')


class MathQuestionDetailOut(BaseModel):
    """题目详情输出模型"""

    session_id: str | None = Field(None, description='会话唯一标识')
    record_id: str = Field(..., description='记录唯一标识')
    question: str = Field(..., description='题目文本')
    question_index: int = Field(..., description='题目在OCR中的下标')
    created_at: str = Field(..., description='创建时间')
    search_result: list[SearchQuestion] | list[SearchQuestion2] | None = Field(
        None, description='搜题结果，SearchQuestion 数据结构的数组'
    )
    solution_idea: list | None = Field(None, description='解题思路')
    knowledge_points: list | None = Field(None, description='关联知识点')
    solution_steps: list | None = Field(None, description='解答过程')
    chat_messages: dict = Field(default_factory=dict, description='聊天消息记录，按创建时间升序排序')


class ExplainDetailItem(BaseModel):
    understanding_agent: str | None = Field(None, description='解题思路')
    knowledge_point_agent: str | None = Field(None, description='关联知识点')
    solution_steps_agent: str | None = Field(None, description='解答过程')


class MathQuestionDetailOut2(BaseModel):
    """题目详情输出模型"""

    session_id: str | None = Field(None, description='会话唯一标识')
    record_id: str = Field(..., description='记录唯一标识')
    question: str = Field(..., description='题目文本')
    question_index: int = Field(..., description='题目在OCR中的下标')
    created_at: str = Field(..., description='创建时间')
    search_result: list[SearchQuestion] | list[SearchQuestion2] | None = Field(
        None, description='搜题结果，SearchQuestion 数据结构的数组'
    )
    chat_messages: dict = Field(default_factory=dict, description='聊天消息记录，按创建时间升序排序')
    explain_result: ExplainDetailItem = Field(..., description='ai讲题流式结果')
