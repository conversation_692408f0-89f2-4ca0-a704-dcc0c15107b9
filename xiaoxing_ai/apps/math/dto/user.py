from pydantic import BaseModel, Field


class UserInfoOut(BaseModel):
    id: int = Field(..., description='自增id')
    name: str = Field(..., description='用户名')
    account: str = Field(..., description='账号')
    gender: int = Field(..., description='性别;1：男;2：女')
    grade_name: str = Field(..., description='年级名称')
    grade_id: int = Field(..., description='年级id')


class UpdateUserInfo(BaseModel):
    grade_: int = Field(..., ge=1, le=12, alias='grade_id', description='年级,k12(1-12)')

    @property
    def grade_id(self):
        """数据库的自增id不从1开始"""
        return self.grade_ + 1


class CheckVersionOut(BaseModel):
    version_name: str = Field(..., description='版本名称（如：1.0.0_013110）')
    version_code: str = Field(..., description='第几个版本（如：10）')
    url: str = Field(..., description='下载链接')
    description: str = Field(..., description='版本发布说明')
    file_size: str = Field(..., description='文件大小（单位：字节）')
    icon: str = Field(..., description='图标链接或base64内容')
    is_forced: bool = Field(..., description='是否强制下载')
    md5: str = Field(..., description='文件MD5码')
