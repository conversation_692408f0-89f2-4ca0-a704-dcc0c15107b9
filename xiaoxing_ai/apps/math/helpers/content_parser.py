from typing import Any, Dict, List, Literal


def parse_math_content(
    data: List[Dict[str, Any]] | None, content_type: Literal['solution_idea', 'knowledge_points', 'solution_steps']
) -> str:
    """
    数学内容解析器，用于解析数学讲题相关内容

    Args:
        data: 要解析的数据列表
        content_type: 内容类型，支持以下值:
            - "solution_idea": 解题思路内容
            - "knowledge_points": 知识点内容
            - "solution_steps": 解题步骤内容

    Returns:
        解析后的文本内容，如果解析失败则返回None

    解析规则:
    - solution_idea: 思路{step_id}：{analysis}
    - knowledge_points: 知识点{point_id}：{name}{description}
    - solution_steps: 步骤{step_id}：{problem}\n{content}
    """
    if not data:
        return ''

    result = []
    for item in data:
        if not isinstance(item, dict):
            continue

        if content_type == 'solution_idea':
            if 'step_id' in item and 'analysis' in item and 'explanation' in item:
                result.append(
                    f'思路{item["step_id"]}：\n【分析】\n{item["analysis"]}\n\n【解释】\n{item["explanation"]}'
                )
        elif content_type == 'knowledge_points':
            if 'point_id' in item and 'name' in item and 'description' in item:
                result.append(f'知识点{item["point_id"]}：\n【名称】{item["name"]}\n\n【描述】\n{item["description"]}')
        elif content_type == 'solution_steps':
            if 'step_id' in item and 'problem' in item and 'content' in item:
                result.append(f'步骤{item["step_id"]}：\n【步骤描述】{item["problem"]}\n\n【内容】\n{item["content"]}')

    return '\n'.join(result)
