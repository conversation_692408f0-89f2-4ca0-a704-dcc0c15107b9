"""
HTML 解析工具类
提供 HTML 内容的解析和处理功能
"""

import asyncio
import re

import httpx
import structlog
from bs4 import BeautifulSoup

from xiaoxing_ai.db import redis_client
from xiaoxing_ai.shared.baidu import BaiduOCR
from xiaoxing_ai.shared.helper import md5

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class HtmlParser:
    """HTML 解析工具类"""

    CACHE_KEY = 'xiaoxing_ai:math:html_parser:{key}'

    @staticmethod
    async def _get_cached_result(cache_key: str) -> str | None:
        """获取缓存结果，封装缓存读取逻辑"""
        try:
            text: str = await redis_client.get(cache_key)
            return text
        except Exception as e:
            logger.error(f'缓存获取失败: {cache_key}, 错误: {str(e)}')

        return None

    @classmethod
    async def extract_text(cls, html_content: str | None) -> str:
        """
        从 HTML 内容中提取纯文本，包括图片的 OCR 识别结果

        Args:
            html_content: HTML 格式的内容

        Returns:
            提取的纯文本内容，包括图片的 OCR 识别结果
        """
        if not html_content:
            return ''

        cache_key = cls.CACHE_KEY.format(key=md5({'html': html_content}))

        cache_result = await cls._get_cached_result(cache_key)
        if cache_result:
            return cache_result

        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找所有图片标签
        images = soup.find_all('img')
        image_tasks = []

        # 处理图片标签，提取图片内容并进行 OCR 识别
        async with httpx.AsyncClient() as client:
            for i, img in enumerate(images):
                # 获取图片的 src 属性（URL）
                src = img.get('src', '')  # type: ignore
                # 去除 URL 中的所有空格
                src = src.strip().replace(' ', '')  # type: ignore
                if src:
                    try:
                        # 下载图片
                        response = await client.get(src, timeout=10.0)
                        response.raise_for_status()
                        image_data = response.content

                        # 创建异步任务进行 OCR 识别
                        task = asyncio.create_task(BaiduOCR().read(image_data))
                        image_tasks.append((img, task))
                    except Exception as e:
                        await logger.aerror(f'处理图片失败: {src}', exc_info=e)

        # 等待所有 OCR 任务完成
        for i, (img, task) in enumerate(image_tasks):
            try:
                # 获取 OCR 识别结果
                ocr_text = await task
                # 将图片标签替换为 OCR 识别结果
                img.replace_with(f'  {ocr_text} ')
            except Exception as e:
                await logger.aerror(f'第 {i + 1} 个图片 OCR 识别失败', exc_info=e)
                # 如果 OCR 识别失败，用占位符替换图片
                img.replace_with(' [图片] ')

        # 提取所有文本，去除 HTML 标签
        text = soup.get_text(separator=' ', strip=True)

        # 清理多余空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        await redis_client.set(cache_key, text, ex=60 * 60 * 24 * 30)  # 缓存一个月

        return text
