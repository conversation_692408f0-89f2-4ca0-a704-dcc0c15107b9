from datetime import datetime

from jinja2 import Template

from .. import constants


def apply_prompt_template(template: Template | None, state: dict) -> str:
    """
    将模板变量应用于提示模板

    Args:
        template: 模板
        state: 要替换的变量

    Returns:
        替换后的模板
    """
    if not template:
        return ''

    # Convert state to dict for template rendering
    state_dict = state.dict() if hasattr(state, 'dict') else state  # type: ignore
    # 添加系统变量
    template_vars = {
        'CURRENT_TIME': datetime.now().strftime(constants.DATE_FORMAT),
        **state_dict,
    }
    return template.render(**template_vars)
