import json
import uuid
from copy import deepcopy
from typing import Any, Dict, Sequence

import structlog
from sqlalchemy import RowMapping, case, func, or_, select
from sqlalchemy.dialects.mysql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from xiaoxing_ai.db.jkxt_models import AuthAccount, Grade, User
from xiaoxing_ai.db.models import (
    AppConfig,
    AppVersion,
    ChatMessage,
    ChatSession,
    HwlQuestionLog,
    MathOcrRecords,
    MathQuestionSearchRecords,
)
from xiaoxing_ai.db.repository import CommonRepository

from .constants import LectureType

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class Repository:
    def __init__(self, db: AsyncSession) -> None:
        self.db = db

    async def get_appconfig_by_key(self, key: str) -> AppConfig | None:
        q = await self.db.execute(
            select(AppConfig).where(
                AppConfig.key_name == key,
                AppConfig.app_name == 'math',
            )
        )
        return q.scalars().first()

    async def get_config_by_key(self, key: str) -> list[dict[str, Any]]:
        """获取list格式的value"""
        stmt = select(func.json_extract(AppConfig.value, '$').label('value_json')).where(
            AppConfig.key_name == key, AppConfig.app_name == 'math'
        )
        q = await self.db.execute(stmt)
        data = q.scalars().first()
        if data is None:
            return []

        try:
            data = json.loads(data)
        except json.decoder.JSONDecodeError:
            return []
        return data

    async def get_max_version(self) -> RowMapping | None:
        """获取软件的最大版本号信息"""
        stmt = (
            select(
                AppVersion.version_code,
                AppVersion.version_name,
                AppVersion.description,
                AppVersion.url,
                AppVersion.icon,
                AppVersion.file_size,
                case(
                    (AppVersion.is_forced == 1, True),
                    (AppVersion.is_forced == 0, False),
                    else_=None,
                ).label('is_forced'),
                AppVersion.md5,
            )
            .where(AppVersion.package_name == 'com.union.searchquestions', AppVersion.deleted_at.is_(None))
            .order_by(AppVersion.version_code.desc())
            .limit(1)
        )
        q = await self.db.execute(stmt)
        return q.mappings().first()


class YYPXRepository:
    def __init__(self, db: AsyncSession) -> None:
        self.db = db

    async def get_account_by_id(self, user_id: int) -> AuthAccount | None:
        q = await self.db.execute(select(AuthAccount).where(AuthAccount.id == user_id))
        return q.scalars().first()

    async def get_account_by_account(self, account: str) -> AuthAccount | None:
        stmt = select(AuthAccount).where(or_(AuthAccount.account == account, AuthAccount.mobile == account))
        q = await self.db.execute(stmt)
        return q.scalars().first()

    async def get_user(self, user_id: int | None) -> User | None:
        if not user_id:
            return None

        q = await self.db.execute(select(User).where(User.id == user_id))
        return q.scalars().first()

    async def get_user_info(self, user_id: int | None) -> RowMapping | None:
        if not user_id:
            return None

        stmt = (
            select(
                User.name,
                User.gender,
                Grade.name.label('grade_name'),
                (Grade.id - 1).label('grade_id'),
            )
            .select_from(User)
            .outerjoin(Grade, User.grade_id == Grade.id)
            .where(User.id == user_id)
        )
        q = await self.db.execute(stmt)
        return q.mappings().first()

    async def get_user_stage(self, user_id: int) -> str:
        # 返回用户的学段: 例如 小学,初中, 高中
        info = await self.get_user_info(user_id)
        stage = '未知'
        if not info:
            return stage

        grade_id = info.grade_id
        if 1 <= grade_id <= 6:
            stage = '小学'
        elif 7 <= grade_id <= 9:
            stage = '初中'
        elif 10 <= grade_id <= 12:
            stage = '高中'
        else:
            stage = '未知'

        return stage


class MathRepository(CommonRepository):
    app_name = 'math'

    async def get_or_create_session(self, session_id: str | None = None) -> str:  # type: ignore[override]
        """重写获取或创建会话方法"""
        if not session_id or session_id == '__default__':
            new_session_id = str(uuid.uuid4())
            instance = ChatSession(session_id=new_session_id)
            self.db.add(instance)
            await self.db.flush()
            await self.db.commit()
            return new_session_id

        # 检查会话是否存在
        q = await self.db.execute(select(ChatSession).where(ChatSession.session_id == session_id))
        data = q.scalars().first()
        if not data:
            # 如果会话不存在，创建新会话
            instance = ChatSession(session_id=session_id)
            self.db.add(instance)
            await self.db.flush()
            await self.db.commit()
            return session_id

        return session_id

    async def get_chat_history_by_type(self, session_id: str, type: LectureType, n: int = 8) -> Sequence[ChatMessage]:
        """获取对应类型的对话记录"""
        stmt = (
            select(ChatMessage)
            .where(
                ChatMessage.session_id == str(session_id),
                ChatMessage.input_type == type,
            )
            .order_by(ChatMessage.created_at.desc())
            .limit(n)
        )

        q = await self.db.execute(stmt)
        data = q.scalars().all()

        # 按创建时间重新排序
        return data[::-1]

    async def get_ocr_record(self, account_id: int, record_id: str) -> MathOcrRecords | None:
        """获取用户ocr记录"""
        stmt = select(MathOcrRecords).where(
            MathOcrRecords.account_id == account_id,
            MathOcrRecords.record_id == record_id,
            MathOcrRecords.deleted_at.is_(None),
        )
        q = await self.db.execute(stmt)
        return q.scalars().first()

    async def create_ocr_record(
        self,
        account_id: int,
        record_id: str,
        question_type: str,
        image_url: str,
        ocr_result: Dict[str, Any],
        is_active: int = 0,
    ) -> MathOcrRecords:
        """创建OCR识别记录"""

        instance = MathOcrRecords(
            account_id=account_id,
            record_id=record_id,
            question_type=question_type,
            image_url=image_url,
            ocr_result=ocr_result,
            is_active=is_active,
        )
        self.db.add(instance)
        await self.db.commit()
        await self.db.refresh(instance)
        return instance

    async def create_question_search_record(
        self,
        account_id: int,
        question: str,
        record_id: str,
        ocr_record_id: str,
        question_index: int,
        source_result: dict,
        session_id: str | None = None,
        image_url: str | None = None,
        search_result: list | None = None,
        knowledge_points: list | None = None,
        solution_idea: list | None = None,
        solution_steps: list | None = None,
    ) -> MathQuestionSearchRecords:
        """创建搜题记录"""

        instance = MathQuestionSearchRecords(
            account_id=account_id,
            session_id=session_id,
            record_id=record_id,
            ocr_record_id=ocr_record_id,
            question=question,
            search_result=search_result,
            image_url=image_url,
            source_result=source_result,
            knowledge_points=knowledge_points,
            solution_idea=solution_idea,
            solution_steps=solution_steps,
            question_index=question_index,
        )
        self.db.add(instance)
        await self.db.commit()
        await self.db.refresh(instance)
        return instance

    async def get_question_search_records_by_session(
        self, account_id: int, session_id: str
    ) -> MathQuestionSearchRecords | None:
        """获取会话的搜题记录"""
        stmt = (
            select(MathQuestionSearchRecords)
            .where(
                MathQuestionSearchRecords.account_id == account_id,
                MathQuestionSearchRecords.session_id == session_id,
            )
            .order_by(MathQuestionSearchRecords.created_at.desc())
        )
        q = await self.db.execute(stmt)
        return q.scalars().first()

    async def get_question_search_records_by_ocr_record_id(self, ocr_record_id: str) -> str | None:
        """获取会话的搜题图片"""
        stmt = (
            select(MathQuestionSearchRecords.image_url)
            .where(
                MathQuestionSearchRecords.ocr_record_id == ocr_record_id,
            )
            .order_by(MathQuestionSearchRecords.id.desc())
        )
        q = await self.db.execute(stmt)
        return q.scalars().first()

    async def update_question_search_record(
        self, account_id: int, record_id: str, **kwargs
    ) -> MathQuestionSearchRecords | None:
        """更新搜题记录的指定字段"""
        stmt = select(MathQuestionSearchRecords).where(
            MathQuestionSearchRecords.account_id == account_id,
            MathQuestionSearchRecords.record_id == record_id,
        )
        q = await self.db.execute(stmt)
        instance = q.scalars().first()

        if instance:
            for key, value in kwargs.items():
                if hasattr(instance, key) and value is not None:
                    setattr(instance, key, value)
            await self.db.commit()
            await self.db.refresh(instance)

        return instance

    async def get_ocr_records(self, account_id: int, limit: int = 10, offset: int = 0) -> Sequence[MathOcrRecords]:
        """获取OCR记录列表"""
        stmt = (
            select(MathOcrRecords)
            .where(
                MathOcrRecords.account_id == account_id,
                MathOcrRecords.is_active == 1,
                MathOcrRecords.deleted_at.is_(None),
            )
            .order_by(MathOcrRecords.id.desc())
            .offset(offset)
            .limit(limit)
        )
        q = await self.db.execute(stmt)
        return q.scalars().all()

    async def get_question_detail(
        self,
        account_id: int,
        ocr_record_id: str,
        question_index: int,
    ) -> MathQuestionSearchRecords | None:
        """根据OCR记录ID和题目索引获取题目详情"""
        stmt = (
            select(MathQuestionSearchRecords)
            .where(
                MathQuestionSearchRecords.account_id == account_id,
                MathQuestionSearchRecords.ocr_record_id == ocr_record_id,
                MathQuestionSearchRecords.question_index == question_index,
                MathQuestionSearchRecords.deleted_at.is_(None),
            )
            .order_by(MathQuestionSearchRecords.id.desc())
            .limit(1)
        )
        q = await self.db.execute(stmt)
        return q.scalars().first()

    async def bulk_update_hwl_question(self, data_list: list[dict]):
        """
        批量更新好未来题目的数据
        :param data_list: 好未来返回结果中的 'questionArr' 数据
        """

        columns = set(HwlQuestionLog.__table__.columns.keys())
        processed_data = []

        for row in data_list:
            new_row = {k: v for k, v in row.items() if k in columns}
            new_row['uid'] = new_row.pop('id')  # id 变为 uid
            processed_data.append(new_row)

        if not processed_data:
            return

        # 构建插入语句
        stmt = insert(HwlQuestionLog).values(processed_data)

        # 定义在重复键时要更新的字段
        on_duplicate_key_stmt = stmt.on_duplicate_key_update(
            question=stmt.inserted.question,  # 示例
            hint=stmt.inserted.hint,  # 示例
            answer=stmt.inserted.answer,  # 示例
            js=stmt.inserted.js,
            css=stmt.inserted.css,
            rank_score=stmt.inserted.rank_score,
            source=stmt.inserted.source,
        )

        await self.db.execute(on_duplicate_key_stmt)
        await self.db.commit()

    async def upsert_question_search_record(
        self,
        account_id: int,
        ocr_record_id: str,
        question_index: int,
        record_id: str,
        **kwargs,
    ) -> MathQuestionSearchRecords:
        instance = await self.get_question_detail(
            account_id=account_id,
            ocr_record_id=ocr_record_id,
            question_index=question_index,
        )
        allowed_update_fields = {'question', 'image_url', 'search_result', 'source_result'}
        if instance:
            # 更新字段，支持可变字段
            for key, value in kwargs.items():
                if key in allowed_update_fields and hasattr(instance, key):
                    setattr(instance, key, value)
        else:
            new_instance = MathQuestionSearchRecords(
                account_id=account_id,
                ocr_record_id=ocr_record_id,
                question_index=question_index,
                record_id=record_id,
                **kwargs,
            )
            self.db.add(new_instance)
            instance = new_instance

        await self.db.commit()
        await self.db.refresh(instance)
        return instance

    async def get_question_by_record_id(self, account_id: int, record_id: str) -> MathQuestionSearchRecords | None:
        """
        通过record_id获取搜题数据
        """
        stmt = select(MathQuestionSearchRecords).where(
            MathQuestionSearchRecords.account_id == account_id, MathQuestionSearchRecords.record_id == record_id
        )
        q = await self.db.execute(stmt)
        instance = q.scalars().first()

        return instance

    async def update_explain_result(self, record_id: str, explain_result: str, lecture_type: LectureType):
        stmt = select(MathQuestionSearchRecords).where(MathQuestionSearchRecords.record_id == record_id)
        q = await self.db.execute(stmt)
        instance = q.scalars().first()

        if instance is None:
            logger.error(f'未找到记录，record_id：{record_id}')
            return

        qs_result = instance.explain_result
        if qs_result is None:
            result = {}
        else:
            result = deepcopy(qs_result)

        result[lecture_type.value] = explain_result

        instance.explain_result = result
        await self.db.commit()
        await self.db.refresh(instance)
