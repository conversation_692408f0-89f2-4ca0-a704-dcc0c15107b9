import json

import structlog

from xiaoxing_ai.db import redis_client
from xiaoxing_ai.shared.helper import md5
from xiaoxing_ai.shared.hwl import TalService

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class SearchQuestionService:
    """搜题服务"""

    @staticmethod
    async def _get_cached_result(cache_key: str) -> dict | None:
        """获取缓存结果，封装缓存读取逻辑"""
        try:
            cached_data: str = await redis_client.get(cache_key)
            if cached_data:
                try:
                    return json.loads(cached_data)
                except json.JSONDecodeError:
                    logger.warning(f'缓存数据损坏: {cache_key}')
        except Exception as e:
            logger.error(f'缓存获取失败: {cache_key}, 错误: {str(e)}')

        return None

    @staticmethod
    async def _cache_search_result(cache_key: str, result: dict):
        """缓存搜索结果，封装缓存写入逻辑"""
        try:
            await redis_client.set(cache_key, json.dumps(result), ex=60 * 60 * 24 * 30)  # 缓存30天
        except Exception as e:
            logger.error(f'缓存写入失败: {cache_key}, 错误: {str(e)}')

    @staticmethod
    def get_highest_similarity_question(search_result_list):
        """
        从搜索结果列表中获取相似度最高的问题

        参数:
            search_result_list: 搜索结果列表

        返回:
            相似度最高的问题文本，如果列表为空则返回空字符串
        """
        if not search_result_list:
            return ''

        # 按相似度排序并获取最高相似度的问题
        highest_similarity_item = max(search_result_list, key=lambda x: x.get('similarity', 0))
        return highest_similarity_item.get('question', '')

    @classmethod
    async def search_question(cls, question: str | None = None, image_base64: str | None = None) -> dict:
        cache_key = f'search_question:{md5({"question": question, "image_base64": image_base64})}'

        # 优先使用缓存结果，减少对搜题服务的调用
        if question:
            cached_result = await cls._get_cached_result(cache_key)
            if cached_result:
                return cached_result

        result = await TalService().search_question(question=question, image_base64=image_base64)

        if question:
            await cls._cache_search_result(cache_key, result)

        return result

    @staticmethod
    async def remove_hint_is_null(question_arr: list[dict]) -> list[dict]:
        """删除结果中讲解为空的"""

        # 过滤掉hint为空或缺失的题目
        filtered = [q for q in question_arr if q.get('hint')]
        if not filtered:
            # 如果过滤掉后没有结果返回原数据的第一条
            return question_arr[:1]

        filtered.sort(key=lambda x: x['rank_score'], reverse=True)
        # 返回第一个
        return filtered[:1]
