import json
import uuid
from typing import Any, Dict, List, Optional, Tuple

from pydantic import ValidationError

from xiaoxing_ai.consts import ChatInputType

from ..dto.agent import MathKnowledgePoint, MathSolutionStep, MathUnderstandingStep
from ..repository import MathRepository


class MathService:
    """数学服务"""

    def __init__(self, repo: MathRepository):
        self.repo = repo

    async def process_math_lecture(self, problem_content: str, agent_data: Dict[str, Any]) -> None:
        """
        处理数学讲题结果，更新搜题记录并创建消息记录

        Args:
            thread_id: 会话ID
            problem_content: 问题内容
            agent_data: 代理返回的数据，包含解题思路和解题步骤等
        """
        # 1. 处理解题思路消息
        await self._process_solution_ideas(problem_content, agent_data)

        # 2. 处理处理知识点消息
        await self._process_knowledge_points(problem_content, agent_data)

        # 3. 处理解题步骤消息
        await self._process_solution_steps(problem_content, agent_data)

    async def _process_solution_ideas(self, problem_content: str, agent_data: Dict[str, Any]) -> None:
        """处理解题思路消息"""
        if 'solution_idea' in agent_data and agent_data['solution_idea']:
            for idea in agent_data['solution_idea']:
                if isinstance(idea, dict) and 'step_id' in idea and 'analysis' in idea and 'session_id' in idea:
                    # 获取或创建会话
                    thread_id = await self.repo.get_or_create_session(idea['session_id'])
                    # 组合规则为：思路{step_id}：{analysis}
                    content = f'思路{idea["step_id"]}：{idea["analysis"]}'
                    # 创建消息记录，使用获取或创建的thread_id
                    await self.repo.create_message(
                        session_id=thread_id,
                        input=problem_content,
                        input_type=ChatInputType.DEFAULT,
                        content=content,
                    )

    async def _process_solution_steps(self, problem_content: str, agent_data: Dict[str, Any]) -> None:
        """处理解题步骤消息"""
        if 'solution_steps' in agent_data and agent_data['solution_steps']:
            for step in agent_data['solution_steps']:
                if (
                    isinstance(step, dict)
                    and 'step_id' in step
                    and 'problem' in step
                    and 'content' in step
                    and 'session_id' in step
                ):
                    # 获取或创建会话
                    thread_id = await self.repo.get_or_create_session(step['session_id'])
                    # 组合规则为：步骤{step_id}：{problem}\n{content}
                    content = f'步骤{step["step_id"]}：{step["problem"]}\n{step["content"]}'
                    await self.repo.create_message(
                        session_id=thread_id,
                        input_type=ChatInputType.DEFAULT,
                        input=problem_content,
                        content=content,
                    )

    async def _process_knowledge_points(self, problem_content: str, agent_data: Dict[str, Any]) -> None:
        """处理知识点消息"""
        if 'knowledge_points' in agent_data and agent_data['knowledge_points']:
            for step in agent_data['knowledge_points']:
                if (
                    isinstance(step, dict)
                    and 'point_id' in step
                    and 'name' in step
                    and 'description' in step
                    and 'session_id' in step
                ):
                    # 获取或创建会话
                    thread_id = await self.repo.get_or_create_session(step['session_id'])
                    # 组合规则为：知识点{point_id}：{name}{description}
                    content = f'知识点{step["point_id"]}：{step["name"]}{step["description"]}'
                    await self.repo.create_message(
                        session_id=thread_id,
                        input_type=ChatInputType.DEFAULT,
                        input=problem_content,
                        content=content,
                    )

    def process_agent_responses(self, complete_responses: Dict[str, List[Any]]) -> Dict[str, Any]:
        """
        处理代理响应数据，提取解题思路、知识点和解题步骤

        Args:
            complete_responses: 包含各个代理完整响应的字典

        Returns:
            处理后的代理数据字典
        """
        agent_data = {}

        # 处理understanding_agent的输出 - 存入solution_idea
        agent_data['solution_idea'] = self._extract_agent_data(complete_responses.get('understanding_agent', []))

        # 处理knowledge_point_agent的输出 - 存入knowledge_points
        agent_data['knowledge_points'] = self._extract_agent_data(complete_responses.get('knowledge_point_agent', []))

        # 处理solution_steps_agent的输出 - 存入solution_steps
        agent_data['solution_steps'] = self._extract_agent_data(complete_responses.get('solution_steps_agent', []))

        return agent_data

    def _extract_agent_data(self, agent_response_list: List[Any]) -> List[Dict[str, Any]] | None:
        """
        从代理响应列表中提取数据

        Args:
            agent_response_list: 代理响应列表

        Returns:
            处理后的数据列表，如果列表为空则返回None
        """
        result_list = []
        for item in agent_response_list:
            if isinstance(item, dict):
                result_list.append(item)
            elif isinstance(item, str) and item.strip():
                try:
                    # 尝试解析可能是字符串形式的JSON
                    parsed_item = json.loads(item)
                    result_list.append(parsed_item)
                except json.JSONDecodeError:
                    # 如果不是有效的JSON，则作为文本项添加
                    result_list.append({'text': item, 'session_id': str(uuid.uuid4())})

        return result_list if result_list else None

    async def update_question_record(
        self,
        account_id: int,
        record_id: str,
        session_id: str,
        problem_content: str,
        agent_data: Dict[str, Any],
    ) -> None:
        """
        更新搜题记录并处理消息记录

        Args:
            account_id: 用户ID
            record_id: 记录ID
            session_id: 会话ID
            problem_content: 问题内容
            agent_data: 代理数据
        """
        # 获取或创建会话
        thread_id = await self.repo.get_or_create_session(session_id)
        # 1. 更新搜题记录
        await self.repo.update_question_search_record(
            account_id=account_id,
            record_id=record_id,
            session_id=thread_id,
            problem=problem_content,
            **agent_data,  # 将提取的数据作为关键字参数传递
        )

        # 2. 处理消息记录
        await self.process_math_lecture(problem_content=problem_content, agent_data=agent_data)

    async def enrich_steps_with_messages(self, steps_data: list) -> list:
        """
        为每个步骤数据添加聊天消息记录，过滤掉最早的一条记录

        参数:
            steps_data: 步骤数据列表

        返回:
            添加了消息记录的步骤数据列表
        """
        result = []
        for step in steps_data:
            session_id = step.get('session_id')
            if session_id:
                # 获取该会话的历史消息（按创建时间升序）
                messages = await self.repo.get_chat_history(session_id, n=8)

                # 添加消息到步骤数据中
                step['messages'] = [{'input': msg.input, 'output': msg.output} for msg in messages]
            result.append(step)
        return result

    def extract_first_valid_step(self, json_str: str, agent_name) -> Tuple[Optional[Dict], str]:
        """
        从流式 JSON 字符串中提取 steps 数组的第一个完整元素（字典）。
        返回 (解析成功的对象 or None, 其余未使用的原始字符串部分)。
        """
        try:
            # 找到 steps 数组的起始
            steps_key = '"steps":'
            start = json_str.find(steps_key)
            if start == -1:
                return None, json_str

            array_start = json_str.find('[', start)
            if array_start == -1:
                return None, json_str

            if '[,' in json_str:
                json_str = json_str.replace('[,', '[')

            decoder = json.JSONDecoder()

            # 解析 [ 后面的部分
            array_content = json_str[array_start + 1 :]
            obj, obj_end = decoder.raw_decode(array_content)

            AGENT_MODEL_MAP = {
                'understanding_agent': MathUnderstandingStep,
                'knowledge_point_agent': MathKnowledgePoint,
                'solution_steps_agent': MathSolutionStep,
            }
            model_cls = AGENT_MODEL_MAP.get(agent_name)

            if model_cls and isinstance(obj, dict):
                try:
                    parsed_obj = model_cls(**obj)
                    used_length = array_start + 1 + obj_end
                    remaining_str1 = json_str[: array_start + 1]
                    remaining_str2 = json_str[used_length:]
                    remaining_str = remaining_str1 + remaining_str2
                    return parsed_obj.dict(), remaining_str  # 或直接返回 parsed_obj
                except ValidationError:
                    pass  # 不匹配则跳过

        except Exception:
            pass

        return None, json_str
