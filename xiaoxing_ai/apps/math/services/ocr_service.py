import json

from fastapi import HTTPException, UploadFile

from xiaoxing_ai.shared.aliyun_ocr import AliyunOCRService
from xiaoxing_ai.shared.baidu import BaiduOCR
from xiaoxing_ai.shared.helper import base64_to_image

from ..constants import MAX_IMAGE_SIZE


class OCRService:
    def __init__(self):
        self.ocr_service = AliyunOCRService()

    async def recognize_single_question(self, image_data: bytes) -> dict:
        return await self.ocr_service.recognize_single_question(image_data)

    async def recognize_multiple_questions(self, image_data: bytes) -> dict:
        return await self.ocr_service.recognize_multiple_questions(image_data)

    async def recognize_paper(self, image_data: bytes) -> dict:
        return await self.ocr_service.recognize_paper(image_data)

    @staticmethod
    def has_valid_subjects(result: dict) -> bool:
        """
        检查阿里OCR结果中是否存在有效的题目内容

        参数:
            result: OCR识别结果

        返回:
            bool: 如果part_info字段中的subject_list不为空则返回True，否则返回False
        """
        if 'Data' in result:
            try:
                # 解析Data字段中的JSON字符串
                data = json.loads(result['Data'])

                # 检查part_info字段
                if 'part_info' in data and isinstance(data['part_info'], list):
                    for part in data['part_info']:
                        if 'subject_list' in part and part['subject_list'] and len(part['subject_list']) > 0:
                            return True
                return False
            except (json.JSONDecodeError, KeyError, TypeError):
                # 解析失败时返回False
                return False
        return False

    @staticmethod
    async def corrected_image(image_data: bytes) -> dict:
        """
        矫正图片
        :param image_data: 图片的字节流
        :return:
        :rtype: dict:
        """
        # 创建OCR客户端并识别文字
        ocr_client = BaiduOCR()
        data = await ocr_client.enhance_document(
            image_data,
            scan_type=3,
            points=None,
            enhance_type=0,
        )

        # 将图片base64转为bytes
        file, _ = base64_to_image(data.image_processed)

        return {
            'log_id': data.log_id,
            'image_bytes': file,
            'image_base64': data.image_processed,
        }

    async def validate_image_size(self, file: UploadFile) -> bytes:
        """验证图片大小并返回图片数据"""

        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail='文件类型必须是图片')

        image_data = await file.read()
        if len(image_data) > MAX_IMAGE_SIZE:
            raise HTTPException(status_code=400, detail=f'图片大小不能超过{MAX_IMAGE_SIZE / (1024 * 1024)}MB')

        return image_data
