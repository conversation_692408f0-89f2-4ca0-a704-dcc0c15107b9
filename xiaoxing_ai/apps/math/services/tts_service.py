import re
from datetime import datetime
from typing import Any, AsyncGenerator, Dict

import structlog

from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.llms import llm_chat_stream

from .. import constants
from ..repository import MathRepository

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class TTSService:
    """
    负责处理文本到语音转换的服务类，包括处理LaTeX公式和生成TTS事件
    """

    def __init__(self, repo: MathRepository, session_id: str):
        # 用于累积消息内容
        self._accumulated_text = ''
        self.app_module = 'math:speechify'
        self._repo = repo
        self._session_id = session_id
        self._sentence_delimiters = ['。', '！', '？']

    def _has_latex(self, text: str) -> bool:
        """
        检查文本是否包含LaTeX公式

        Args:
            text: 要检查的文本

        Return:
            布尔值，表示是否包含LaTeX公式
        """
        # 检测 LaTeX 公式的常见开始标记
        latex_pattern = r'(\$\$|\$|\\\(|\\\[)'
        return bool(re.search(latex_pattern, text))

    async def speechify(self, text: str) -> AsyncGenerator[str, None]:
        """
        将LaTeX公式转换为口语化文本，以流式方式返回结果

        Args:
            text: 包含LaTeX公式的文本

        Returns:
            转换后的口语化文本流
        """
        # 直接使用Jinja2模板渲染，包含当前时间
        template = await self._repo.get_model_prompt('latex_to_speech')
        if not template:
            # 如果模板不存在，返回原始文本
            yield text
            return

        prompt = template.render(CURRENT_TIME=datetime.now().strftime(constants.DATE_FORMAT))
        llm_config = await self._repo.get_ai_config_by_module(self.app_module)
        if not llm_config:
            # 模型实例未配置，返回原始文本
            await logger.aerror(f'LaTeX流式转换失败，未找到{self.app_module}模块对应的模型实例配置')
            yield text
            return

        messages = [{'role': 'system', 'content': prompt}, {'role': 'user', 'content': text}]
        params = llms.get_params(llm_config, messages)
        async with llms.get_llm(llm_config) as llm:
            try:
                # 调用大模型流式转换文本
                stream = await llm_chat_stream(llm, params)

                # 流式返回结果
                async for chunk in stream:
                    if chunk.choices and chunk.choices[0].delta.content:
                        yield chunk.choices[0].delta.content
            except Exception as e:
                await logger.aerror(f'LaTeX流式转换失败: {str(e)}')
                # 转换失败时返回原文本
                yield text

    async def truncate_for_tts(self, text: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理文本并生成TTS事件

        Args:
            text: 要处理的文本
        Returns:
            生成的TTS事件
        """

        # 累积新文本并处理
        self._accumulated_text += text
        # 检查是否有句子结束标志
        split_pattern = f'([{"".join(self._sentence_delimiters)}])'
        sentences = re.split(split_pattern, self._accumulated_text)

        if len(sentences) >= 2:
            # 内联提取完整句子和剩余文本
            complete_sentences = []
            for i in range(0, len(sentences) - 1, 2):
                if i + 1 < len(sentences):
                    complete_sentences.append(sentences[i] + sentences[i + 1])
            remaining_text = sentences[-1] if len(sentences) % 2 == 1 else ''

            # 处理每个完整句子
            for sentence in complete_sentences:
                async for chunk in self._process_text_chunk(sentence):
                    yield chunk

            # 更新累积文本
            self._accumulated_text = remaining_text

    async def _process_text_chunk(self, text: str) -> AsyncGenerator[Dict[str, Any], None]:
        """统一处理文本块"""

        if not text.strip():
            return

        if not self._has_latex(text):
            yield {
                'thread_id': self._session_id,
                'content': text,
            }
            return

        async for chunk in self.speechify(text):
            if chunk.strip():
                yield {
                    'thread_id': self._session_id,
                    'content': chunk,
                }

    async def flush(self) -> AsyncGenerator[Dict[str, Any], None]:
        """清空缓存"""
        if self._accumulated_text.strip():
            if self._accumulated_text[-1] not in self._sentence_delimiters:
                self._accumulated_text += '。'  # 默认补充句号
            async for chunk in self._process_text_chunk(self._accumulated_text):
                yield chunk
            self._accumulated_text = ''
