from fastapi import APIRouter, Depends

from ..deps import get_current_active_user
from . import auth_views, lecture_views, ocr_views, qa_views, search_views, tts_views, user_views

# 创建主路由器
router = APIRouter(prefix='/math', tags=['爱数学'])

# 包含各个子路由器
router.include_router(auth_views.router)
router.include_router(user_views.router, dependencies=[Depends(get_current_active_user)])
router.include_router(ocr_views.router, dependencies=[Depends(get_current_active_user)])
router.include_router(search_views.router, dependencies=[Depends(get_current_active_user)])
router.include_router(qa_views.router, dependencies=[Depends(get_current_active_user)])
router.include_router(lecture_views.router, dependencies=[Depends(get_current_active_user)])
router.include_router(tts_views.router, dependencies=[Depends(get_current_active_user)])
