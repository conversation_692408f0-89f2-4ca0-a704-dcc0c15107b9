from fastapi import APIRouter, Depends
from fastapi.security import HTTPAuthorizationCredentials

from xiaoxing_ai.shared.api import APIException, ApiResponse, ErrorCode

from ..auth import authenticate_user, create_access_token, decode_jwt_token
from ..deps import YYPXSessionDep, auth_scheme, get_current_active_user
from ..dto.auth import LoginIn, LoginOut, RefreshOut

router = APIRouter()


@router.post('/login', summary='登录')
async def login(data: LoginIn, db: YYPXSessionDep) -> ApiResponse[LoginOut]:
    """使用即刻学堂的账号登录"""

    user = await authenticate_user(db, data.account, data.password.get_secret_value())

    if user is None:
        raise APIException(
            detail='请检查用户名或密码',
            code=ErrorCode.FIELD_ERROR,
            headers={'WWW-Authenticate': 'Bearer'},
        )
    user = await get_current_active_user(user)

    subject = {'id': user.id}

    access_token = create_access_token(subject)
    refresh_token = create_access_token(subject, token_type='refresh')

    return ApiResponse(
        data={
            'access_token': access_token,
            'refresh_token': refresh_token,
        }
    )


@router.post('/refresh')
def refresh(
    token: HTTPAuthorizationCredentials = Depends(auth_scheme),
) -> ApiResponse[RefreshOut]:
    # 创建新的 access token 并且生成新的 refresh_token
    payload = decode_jwt_token(token.credentials, expected_type='refresh')

    subject = payload['subject']

    access_token = create_access_token(subject)
    refresh_token = create_access_token(subject, token_type='refresh')

    return ApiResponse(
        data={
            'access_token': access_token,
            'refresh_token': refresh_token,
        }
    )
