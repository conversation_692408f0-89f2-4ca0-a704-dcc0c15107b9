import uuid

import structlog
from fastapi import APIRouter, HTTPException, Path, Query
from fastapi.responses import StreamingResponse
from langchain_core.runnables import RunnableConfig
from langfuse.callback import Callback<PERSON>andler
from langfuse.decorators import langfuse_context, observe
from openai.types.chat import ChatCompletionUserMessageParam

from xiaoxing_ai.db import AsyncSessionLocal
from xiaoxing_ai.shared.api import ApiResponse
from xiaoxing_ai.shared.llms import ModelName, azure_openai, llm_chat_stream
from xiaoxing_ai.shared.sse import make_sse_event

from ..agents import workflow
from ..constants import LectureType
from ..deps import AuthDep, SessionDep, YYPXSessionDep
from ..dto.agent import MathLectureIn
from ..helpers.html_parser import HtmlParser
from ..prompts.templates import apply_prompt_template
from ..repository import MathRepository, YYPXRepository

router = APIRouter(tags=['AI讲解'])
logger = structlog.stdlib.get_logger('xiaoxing_ai')


@router.post('/lecture', summary='AI讲题')
@observe(capture_input=False)
async def process_math_lecture(
    user: AuthDep,
    db: SessionDep,
    data: MathLectureIn,
):
    """
    AI讲题接口，包含协调器和多个专业智能体

    参数:
        data: 讲题请求数据，包含 current_problem 和 record_id
        db: 数据库会话

    返回:
        智能体响应的流式输出
    """
    repo = MathRepository(db)

    # 获取或创建会话
    thread_id = await repo.get_or_create_session(data.thread_id)
    langfuse_context.update_current_trace(session_id=thread_id)
    langfuse_handler = CallbackHandler(session_id=thread_id)

    # # 解析HTML获取纯文本
    question_text = await HtmlParser.extract_text(data.current_problem.question)
    hint_text = await HtmlParser.extract_text(data.current_problem.explanation)
    answer_text = await HtmlParser.extract_text(data.current_problem.answer)

    math_knowledge = await repo.get_model_prompt('math_knowledge_points')
    math_solution_steps = await repo.get_model_prompt('math_solution_steps')
    math_understanding = await repo.get_model_prompt('math_understanding')

    state = {
        'current_problem': {
            'problem': data.current_problem.problem,
            'grade': data.current_problem.grade,
            'question_text': question_text,
            'hint_text': hint_text,
            'answer_text': answer_text,
        }
    }

    prompts = {
        'understanding': apply_prompt_template(math_understanding, state),
        'solution_steps': apply_prompt_template(math_solution_steps, state),
        'knowledge_points': apply_prompt_template(math_knowledge, state),
    }

    # 获取AI模型实例数据
    llm_config = await repo.get_ai_config_by_module('math:lecture')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    await logger.adebug(f'AI模型服务商实例: {llm_config.model_dump()}')

    @observe(capture_output=False)
    async def event_generator(llm_config, prompts, state):
        config = RunnableConfig(
            callbacks=[langfuse_handler],
            configurable={'thread_id': thread_id, 'llm_config': llm_config, 'prompts': prompts, 'state': state},
        )
        async with AsyncSessionLocal() as db:
            repo = MathRepository(db)

            async for result in workflow.execute(config):
                fields = {
                    'understanding_agent': 'solution_idea',
                    'knowledge_point_agent': 'knowledge_points',
                    'solution_steps_agent': 'solution_steps',
                }

                for k, v in result.items():
                    steps = [out.model_dump() | {'session_id': str(uuid.uuid4())} for out in v.steps]
                    event = {'agent': k, 'thread_id': thread_id, 'data': steps, 'finish_reason': None}

                    yield make_sse_event(event, event_type='message_chunk')
                    yield make_sse_event(event | {'data': [], 'finish_reason': 'stop'}, event_type='message_chunk')

                    await repo.update_question_search_record(
                        account_id=user.id,
                        record_id=data.record_id or '',
                        session_id=thread_id,
                        problem=data.current_problem.problem,
                        **{fields[k]: steps},
                    )

    try:
        return StreamingResponse(event_generator(llm_config, prompts, state), media_type='text/event-stream')
    except Exception as e:
        await logger.aerror(f'AI讲解失败: {str(e)}')
        return ApiResponse(code=500, msg=f'AI讲解失败: {str(e)}')


@router.get('/lecture/{lecture_type}', summary='获取各类型的ai讲题')
@observe(capture_input=False)
async def ai_lecture(
    user: AuthDep,
    db: SessionDep,
    yypx_db: YYPXSessionDep,
    lecture_type: LectureType = Path(..., description='讲题类型'),
    ocr_record_id: uuid.UUID = Query(..., description='ocr_id'),
    index: int = Query(0, description='问题下标'),
):
    uid = str(uuid.uuid4())
    langfuse_context.update_current_trace(session_id=uid)

    yypx_repo = YYPXRepository(yypx_db)
    stage = await yypx_repo.get_user_stage(user.id)

    repo = MathRepository(db)

    instance = await repo.get_question_detail(user.id, str(ocr_record_id), index)
    if not instance:
        return ApiResponse(code=404, msg='未查询到数据')

    # 默认取第一条数据
    if not instance.search_result:
        return ApiResponse(code=404, msg='不存在搜题结果')

    result = instance.search_result  # search_result is a json array in MySQL
    result: dict = result[0]
    # # 解析HTML获取纯文本
    question_text = await HtmlParser.extract_text(result['question'])
    hint_text = await HtmlParser.extract_text(result['explanation'])
    answer_text = await HtmlParser.extract_text(result['answer'])

    state = {
        'current_problem': {
            'grade': stage,
            'question_text': question_text,
            'hint_text': hint_text,
            'answer_text': answer_text,
        }
    }
    template = await repo.get_model_prompt(f'math_{lecture_type.name.lower()}')
    prompt = apply_prompt_template(template, state)

    params = {
        'model': ModelName.GPT_4O,
        'temperature': 0.7,
        'messages': [
            ChatCompletionUserMessageParam(content=prompt, role='user'),
        ],
        'stream': True,
    }

    @observe(as_type='generation', name=f'math_lecture_{lecture_type.name.lower()}')
    async def generator():
        buffer = []
        res = await llm_chat_stream(azure_openai, params)
        async for chunk in res:
            if not chunk or not chunk.choices:
                continue

            chunk_content = chunk.choices[0].delta.content or ''
            if not chunk_content:
                continue

            buffer.append(chunk_content)
            # 直接发送内容片段
            yield make_sse_event({'content': chunk_content, 'record_id': instance.record_id}, event_type='message')

        content = ''.join(buffer)

        async with AsyncSessionLocal() as db:
            repo = MathRepository(db)
            await repo.update_explain_result(instance.record_id, content, lecture_type)

    return StreamingResponse(generator(), media_type='text/event-stream')
