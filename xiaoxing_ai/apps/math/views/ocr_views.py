import datetime
import json
from pathlib import Path
from typing import List

import structlog
from fastapi import APIRouter, File, Query, UploadFile
from fastapi import Path as QueryPath

from xiaoxing_ai.shared.api import APIException, ApiResponse, ErrorCode
from xiaoxing_ai.shared.oss import oss

from ..constants import OCRType
from ..deps import AuthDep, SessionDep
from ..dto.ocr import OCRRecordOut, SaveOCRRecordRequest
from ..repository import MathRepository
from ..services import OCRService

router = APIRouter()
ocr_service = OCRService()
logger = structlog.stdlib.get_logger('xiaoxing_ai')


@router.get('/ocr/records', summary='获取OCR记录列表', response_model=ApiResponse[List[OCRRecordOut]])
async def get_ocr_records(
    user: AuthDep,
    db: SessionDep,
    limit: int = Query(10, description='每页记录数'),
    offset: int = Query(0, description='偏移量'),
):
    """
    获取OCR记录列表，按创建时间倒序排列

    参数:
        limit: 每页记录数，默认10条
        offset: 偏移量，默认0
        db: 数据库会话

    返回:
        OCR记录列表
    """
    try:
        repo = MathRepository(db)

        records = await repo.get_ocr_records(account_id=user.id, limit=limit, offset=offset)

        result = []
        for record in records:
            search_record_image_url = await repo.get_question_search_records_by_ocr_record_id(record.record_id)
            # 优先使用题目图片，不存在才使用拍题的图片
            image_url = search_record_image_url if search_record_image_url is not None else record.image_url

            # 现在会保存ocr的所有数据,所以需要在这里取出只使用的字段
            ocr_result = record.ocr_result
            data = json.loads(ocr_result['Data'])
            ocr_result['Data'] = json.dumps(
                {
                    'part_info': data['part_info'],
                    'angle': data.get('angle', 0),
                },
                ensure_ascii=False,
            )

            result.append(
                OCRRecordOut(
                    record_id=record.record_id,
                    question_type=record.question_type,
                    cover_image_url=oss.cdn_url(image_url),
                    image_url=oss.cdn_url(record.image_url),
                    created_at=record.created_at.strftime('%Y年%m月%d日 %H:%M'),
                    ocr_result=ocr_result,
                )
            )

        return ApiResponse(data=result)
    except Exception as e:
        logger.error(f'获取OCR记录列表失败: {str(e)}')
        raise APIException(code=ErrorCode.SERVER_ERROR, detail=f'获取OCR记录列表失败: {str(e)}')


@router.post('/ocr/save-record', summary='保存OCR记录', deprecated=True)
async def save_ocr_record(user: AuthDep, db: SessionDep, data: SaveOCRRecordRequest):
    """
    保存OCR记录到数据库

    前端可以在获取OCR结果后，修改结果并调用此接口保存
    """
    # 初始化Repository
    repo = MathRepository(db)

    try:
        # 检查是否存在有效的题目内容
        if data.question_type != OCRType.SINGLE.name.lower():
            has_subjects = ocr_service.has_valid_subjects(data.ocr_result)

            if not has_subjects:
                logger.warning(f'OCR识别结果中没有有效的题目内容: {data.record_id}')
                return ApiResponse(code=400, msg='OCR识别结果中没有有效的题目内容')

        record = await repo.get_ocr_record(user.id, data.record_id)

        if not record:
            # 保存OCR记录
            await repo.create_ocr_record(
                account_id=user.id,
                record_id=data.record_id,
                question_type=data.question_type,
                image_url=data.image_url,
                ocr_result=data.ocr_result,
                is_active=1,
            )
        else:
            # 存在则使用用户选择的数据,覆盖掉源数据
            record.ocr_result = data.ocr_result
            record.is_active = 1
            await db.commit()

        return ApiResponse(msg='保存成功')
    except Exception as e:
        logger.error(f'保存OCR记录失败: {str(e)}')
        return ApiResponse(code=500, msg=f'保存OCR记录失败: {str(e)}')


@router.post('/ocr/{ocr_type}', summary='OCR识别')
async def recognize_question(
    user: AuthDep,
    db: SessionDep,
    ocr_type: OCRType = QueryPath(
        ..., description='ocr类型; single-question: 单题; multiple-questions: 多题; paper: 试卷;'
    ),
    file: UploadFile = File(..., description='题目图片'),
):
    """识别单题"""
    repo = MathRepository(db)

    # 验证图片大小
    try:
        image_data = await ocr_service.validate_image_size(file)
    except Exception as e:
        logger.error(f'图片验证失败: {str(e)}')
        return ApiResponse(code=500, msg=f'图片验证失败: {str(e)}')

    try:
        # 矫正图片
        cr_data = await ocr_service.corrected_image(image_data)
        image_data = cr_data['image_bytes']

        # 先判断是否时数学题
        # is_math = await MathQuestionDetector().has_math_content(image_data)
        # if not is_math:
        #     return ApiResponse(code=400, msg='当前不是数学题目，请重新拍题!')

        # 调用OCR服务
        match ocr_type:
            case OCRType.SINGLE:
                result = await ocr_service.recognize_multiple_questions(image_data)
            case OCRType.MULTI:
                result = await ocr_service.recognize_multiple_questions(image_data)
            case OCRType.PAPER:
                result = await ocr_service.recognize_paper(image_data)
            case _:
                return ApiResponse(code=400, msg='搜题类型错误')

    except Exception as e:
        logger.error(f'OCR单题识别失败: {str(e)}')
        return ApiResponse(code=500, msg=f'OCR识别失败: {str(e)}')
    question_type = ocr_type.name.lower()
    # 上传图片到OSS
    try:
        oss_key = oss.upload_data(
            image_data,  # 矫正后的
            prefix=f'math/{question_type}',
            obj_key=cr_data['log_id'],
            suffix=Path(file.filename).suffix,  # type: ignore
        )
    except Exception as e:
        logger.error(f'图片上传失败: {str(e)}')
        oss_key = ''

    # 保存OCR记录
    obj = await repo.create_ocr_record(
        account_id=user.id,
        record_id=result.get('RequestId', ''),
        question_type=question_type,
        image_url=oss_key,
        ocr_result=result,
    )

    # 检查是否存在有效的题目内容
    # 单题不在判断,因为可能不会返回 `part_info`
    if ocr_type != OCRType.SINGLE:
        has_subjects = ocr_service.has_valid_subjects(result)

        if not has_subjects:
            # 没有有效数据就软删除
            obj.deleted_at = datetime.datetime.now()
            await db.commit()

            logger.warning(f'OCR单题识别结果中没有有效的题目内容: {result.get("RequestId", "")}')
            return ApiResponse(code=400, msg='OCR识别结果中没有有效的题目内容')

    # 返回OCR结果和图片URL，前端可以修改OCR结果后调用保存接口
    return ApiResponse(
        data={
            'record_id': result.get('RequestId', ''),
            'image_url': oss_key,
            'image_base64': cr_data['image_base64'],
            'ocr_result': result,
            'question_type': question_type,
        }
    )
