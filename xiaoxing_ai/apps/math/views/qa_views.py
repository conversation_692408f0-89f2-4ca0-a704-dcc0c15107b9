from typing import Any

import structlog
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from langfuse.decorators import langfuse_context, observe

from xiaoxing_ai.consts import ChatEventType
from xiaoxing_ai.shared import llms
from xiaoxing_ai.shared.llms import LLMConfig
from xiaoxing_ai.shared.sse import make_sse_event
from xiaoxing_ai.shared.xfyun import XFAuditService

from ..constants import LectureType
from ..deps import AuthDep, SessionDep
from ..dto.agent import MathQAIn, MathQAIn2, MathQAMessage
from ..helpers import parse_math_content
from ..prompts.templates import apply_prompt_template
from ..repository import MathRepository
from ..services import TTSService

router = APIRouter()
logger = structlog.stdlib.get_logger('xiaoxing_ai')


@router.post('/qa', summary='问答(流式返回)')
@observe(capture_input=False)
async def get_qa(
    user: AuthDep,
    db: SessionDep,
    data: MathQAIn,
) -> StreamingResponse:
    repo = MathRepository(db)

    if not data.query.strip():
        raise HTTPException(status_code=400, detail='问题不能为空')

    session_id = await repo.get_or_create_session(str(data.session_id) if data.is_item else str(data.group_id))
    if not session_id:
        raise HTTPException(status_code=404, detail=f'会话{data.session_id}不存在')

    langfuse_context.update_current_trace(session_id=session_id)

    harmful, audit_description = await XFAuditService().audit(data.query)

    result = await repo.get_question_search_records_by_session(account_id=user.id, session_id=str(data.group_id))
    lecture_result_list = []
    if result:
        # 有数据则为麦克风对话，需要带入讲解结果的3个上下文
        # 解析 qsr.solution_idea、 qsr.knowledge_points、qsr.solution_steps的内容为字符串
        match data.type:
            case LectureType.IDEA:
                solution_idea_text = parse_math_content(result.solution_idea, 'solution_idea')  # type: ignore
                lecture_result_list.append(solution_idea_text)
            case LectureType.KNOWLEDGE:
                knowledge_points_text = parse_math_content(result.knowledge_points, 'knowledge_points')  # type: ignore
                lecture_result_list.append(knowledge_points_text)
            case LectureType.STEPS:
                solution_steps_text = parse_math_content(result.solution_steps, 'solution_steps')  # type: ignore
                lecture_result_list.append(solution_steps_text)

    template = await repo.get_model_prompt('math_qa')
    # 将模板变量组装成字典
    template_vars = {
        'harmful': harmful,
        'audit_description': audit_description,
        'context': ''.join(lecture_result_list),
    }

    # 使用统一方法渲染
    prompt = apply_prompt_template(template, template_vars)
    context = [dict(role='system', content=prompt)]

    history = await repo.get_chat_history(session_id, 8)
    for msg in history:
        context.append(dict(role='user', content=msg.input))
        context.append(dict(role='assistant', content=msg.output))

    llm_config = await repo.get_ai_config_by_module('math:qa')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    params = llms.get_params(llm_config, [*context, dict(role='user', content=data.query)])

    @observe(as_type='generation', capture_output=False)
    async def generator(llm_config: LLMConfig, params: dict[str, Any], db_session: SessionDep):
        math_repo = MathRepository(db_session)
        try:
            tts_service = TTSService(math_repo, session_id)
            buffer = []
            async with llms.get_llm(llm_config) as llm:
                r = await llms.llm_chat_stream(llm, params)
                async for chunk in r:
                    # azure_openai 第一个可能为空数组
                    if not chunk or not chunk.choices:
                        continue
                    if content := chunk.choices[0].delta.content:
                        buffer.append(content)

                        # 为内容生成 TTS 事件
                        async for tts_event in tts_service.truncate_for_tts(content):
                            yield make_sse_event(tts_event, event_type='tts_chunk')

                        msg = MathQAMessage(session_id=session_id, content=content)

                        # 豆包的 finish_reason 定义
                        # https://api.volcengine.com/api-docs/view?action=ChatCompletionsText&serviceCode=ark&version=2024-01-01#%E5%93%8D%E5%BA%94%E5%8F%82%E6%95%B0
                        if chunk.choices[0].finish_reason == 'content_filter':
                            content = '抱歉，不如我们再换个话题聊聊吧~'
                            msg.content = content
                            msg.type = ChatEventType.CLEAR

                        yield make_sse_event(msg, event_type='message_chunk')

                # 处理剩余内容
                async for tts_event in tts_service.flush():
                    yield make_sse_event(tts_event, event_type='tts_chunk')

            await math_repo.create_message(
                session_id=session_id, input_type=data.type, input=data.query, content=''.join(buffer)
            )
            langfuse_context.update_current_observation(output=''.join(buffer))
        except Exception as e:
            await logger.aerror(f'生成过程出现异常: {str(e)}')
        finally:
            await math_repo.db.aclose()  # 主动close

    try:
        return StreamingResponse(
            generator(llm_config, params, db),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )
    except Exception as e:
        await logger.aexception('Failed to generate response', exc_info=e)
        raise HTTPException(status_code=500, detail='Failed to generate response')


@router.post('/qa2', summary='问答(流式返回)2')
@observe(capture_input=False)
async def get_qa2(
    user: AuthDep,
    db: SessionDep,
    data: MathQAIn2,
) -> StreamingResponse:
    repo = MathRepository(db)

    if not data.query.strip():
        raise HTTPException(status_code=400, detail='问题不能为空')

    session_id = await repo.get_or_create_session(str(data.session_id))
    if not session_id:
        raise HTTPException(status_code=404, detail=f'会话{data.session_id}不存在')

    langfuse_context.update_current_trace(session_id=session_id)

    harmful, audit_description = await XFAuditService().audit(data.query)

    record = await repo.get_question_by_record_id(account_id=user.id, record_id=session_id)

    lecture_result_list = []
    if record:
        if explain_result := record.explain_result:
            lecture_result_list.append(explain_result.get(data.type, ''))

    template = await repo.get_model_prompt('math_qa')
    # 将模板变量组装成字典
    template_vars = {
        'harmful': harmful,
        'audit_description': audit_description,
        'context': ''.join(lecture_result_list),
    }

    # 使用统一方法渲染
    prompt = apply_prompt_template(template, template_vars)
    context = [dict(role='system', content=prompt)]

    history = await repo.get_chat_history_by_type(session_id, data.type, 8)
    for msg in history:
        context.append(dict(role='user', content=msg.input))
        context.append(dict(role='assistant', content=msg.output))

    llm_config = await repo.get_ai_config_by_module('math:qa')
    if not llm_config:
        raise HTTPException(status_code=500, detail='未找到对应模块的 AI 配置')

    params = llms.get_params(llm_config, [*context, dict(role='user', content=data.query)])

    @observe(as_type='generation', capture_output=False)
    async def generator(llm_config: LLMConfig, params: dict[str, Any], db_session: SessionDep):
        math_repo = MathRepository(db_session)
        try:
            tts_service = TTSService(math_repo, session_id)
            buffer = []
            async with llms.get_llm(llm_config) as llm:
                r = await llms.llm_chat_stream(llm, params)
                async for chunk in r:
                    # azure_openai 第一个可能为空数组
                    if not chunk or not chunk.choices:
                        continue
                    if content := chunk.choices[0].delta.content:
                        buffer.append(content)

                        # 为内容生成 TTS 事件
                        async for tts_event in tts_service.truncate_for_tts(content):
                            yield make_sse_event(tts_event, event_type='tts_chunk')

                        msg = MathQAMessage(session_id=session_id, content=content)

                        # 豆包的 finish_reason 定义
                        # https://api.volcengine.com/api-docs/view?action=ChatCompletionsText&serviceCode=ark&version=2024-01-01#%E5%93%8D%E5%BA%94%E5%8F%82%E6%95%B0
                        if chunk.choices[0].finish_reason == 'content_filter':
                            content = '抱歉，不如我们再换个话题聊聊吧~'
                            msg.content = content
                            msg.type = ChatEventType.CLEAR

                        yield make_sse_event(msg, event_type='message_chunk')

                # 处理剩余内容
                async for tts_event in tts_service.flush():
                    yield make_sse_event(tts_event, event_type='tts_chunk')

            await math_repo.create_message(
                session_id=session_id, input_type=data.type, input=data.query, content=''.join(buffer)
            )
            langfuse_context.update_current_observation(output=''.join(buffer))
        except Exception as e:
            await logger.aerror(f'生成过程出现异常: {str(e)}')
        finally:
            await math_repo.db.aclose()  # 主动close

    try:
        return StreamingResponse(
            generator(llm_config, params, db),
            media_type='text/event-stream',
            headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'},
        )
    except Exception as e:
        await logger.aexception('Failed to generate response', exc_info=e)
        raise HTTPException(status_code=500, detail='Failed to generate response')
