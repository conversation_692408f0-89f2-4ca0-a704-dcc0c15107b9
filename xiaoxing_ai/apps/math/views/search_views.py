import datetime
import uuid
from collections import defaultdict

import structlog
from fastapi import APIRouter, Query
from jinja2 import Template

from xiaoxing_ai.shared.api import APIException, ApiResponse, ErrorCode
from xiaoxing_ai.shared.helper import base64_to_image
from xiaoxing_ai.shared.hwl import TalException, TalNotQuestionException, TalParamsException
from xiaoxing_ai.shared.oss import oss

from ..deps import AuthDep, SessionDep
from ..dto.question import (
    ExplainDetailItem,
    MathQuestionDetailOut,
    MathQuestionDetailOut2,
    Metadata,
    SearchQuestion,
    SearchQuestion2,
    SearchQuestionIn,
    SearchQuestionOut,
    SearchQuestionOut2,
)
from ..repository import MathRepository, Repository
from ..services import MathService, SearchQuestionService

router = APIRouter()
logger = structlog.stdlib.get_logger('xiaoxing_ai')


@router.post('/search-question', summary='拍照搜题')
async def search_question(
    data: SearchQuestionIn,
    user: AuthDep,
    db: SessionDep,
) -> ApiResponse[SearchQuestionOut]:
    """
    搜题接口，支持文本或PNG格式图片，优先用question
    """
    cfg_repo = Repository(db)
    repo = MathRepository(db)
    record_id = str(uuid.uuid4())

    if not data.question and not data.image_base64:
        return ApiResponse(code=400, msg='question和image_base64参数必须至少提供一个')

    search_questions: list[SearchQuestion] = []
    hwl_result = {}
    try:
        # 调用搜题服务
        result = await SearchQuestionService.search_question(question=data.question, image_base64=data.image_base64)
        css = await cfg_repo.get_config_by_key('hwl_search_css')
        js = await cfg_repo.get_config_by_key('hwl_search_js')
        # 验证结果
        if result and isinstance(result, dict):
            hwl_result = result
            # 去除解析为空的
            # result_list = await SearchQuestionService.remove_hint_is_null(result['questionArr'])
            for item in result['questionArr']:
                search_questions.append(
                    SearchQuestion(
                        id=item['id'],
                        question=item['question'],
                        explanation=item['hint'],
                        answer=item['answer'],
                        similarity=item.get('rank_score', 100),
                        metadata=Metadata(
                            css=[*item['css'], *css],  # 多添加一项自己的文件
                            js=js,  # 不使用 好未来的js文件
                        ),
                    )
                )
            await repo.bulk_update_hwl_question(result['questionArr'])

    # 搜题失败时，search_questions和search_result_dict保持为空列表
    except TalNotQuestionException as e:
        logger.error(f'未搜索到题目: {str(e)}')
    except TalParamsException as e:
        logger.error(f'搜题参数错误: {str(e)}')
    except TalException as e:
        logger.error(f'搜题失败: {str(e)}')

    url = None
    if data.image_base64:
        # base64 转图片后保存至oss
        file, suffix = base64_to_image(data.image_base64)
        today = datetime.date.today()
        # 还需要放入用户id
        oss_path = f'/search/{today.year}/{today.month}/{today.day}'
        url = oss.upload_data(
            file,
            obj_key=record_id,
            prefix=f'math/{oss_path}',
            suffix=suffix or '.png',
        )

    # 将 Pydantic 模型转换为字典列表
    search_result_list = [q.model_dump() for q in search_questions]

    # 从搜索结果中获取相似度最高的问题
    best_question = SearchQuestionService.get_highest_similarity_question(search_result_list)
    # 先使用接口返回的文本,在找到相似度最高的问题，否则使用空字符串
    final_question = hwl_result.get('image_txt') or best_question or ''

    # 先查询是否存在记录
    instance = await repo.get_question_detail(
        account_id=user.id,
        ocr_record_id=data.ocr_record_id,
        question_index=data.question_index,
    )

    if instance:
        # 如果记录存在，则更新
        instance.question = final_question
        instance.image_url = url
        instance.search_result = search_result_list  # pyright: ignore[reportAttributeAccessIssue]
        instance.source_result = hwl_result
        await db.commit()

        record_id = instance.record_id
    else:
        # 如果记录不存在，则创建
        new_record = await repo.create_question_search_record(
            account_id=user.id,
            record_id=record_id,
            ocr_record_id=data.ocr_record_id,
            question=final_question,
            image_url=url,
            search_result=search_result_list,
            source_result=hwl_result,
            question_index=data.question_index,
        )
        record_id = new_record.record_id

    return ApiResponse(data={'questions': search_questions, 'record_id': record_id})


@router.get('/question/detail', summary='获取搜过题的题目详情', response_model=ApiResponse[MathQuestionDetailOut])
async def get_question_detail(
    user: AuthDep,
    db: SessionDep,
    ocr_record_id: str = Query(..., description='OCR记录ID'),
    question_index: int = Query(..., description='题目在OCR中的下标'),
):
    """
    根据OCR记录ID和题目索引获取题目详情

    参数:
        ocr_record_id: OCR记录ID
        question_index: 题目在OCR中的下标
        db: 数据库会话

    返回:
        题目详情
    """
    try:
        repo = MathRepository(db)
        math_service = MathService(repo)

        record = await repo.get_question_detail(
            account_id=user.id,
            ocr_record_id=ocr_record_id,
            question_index=question_index,
        )

        if not record:
            raise APIException(code=ErrorCode.NOT_FOUND, detail='未找到对应的题目详情')

        # 将 JSON 数据转换为 SearchQuestionOut 对象列表
        search_result = None
        if record.search_result:
            try:
                search_result = [SearchQuestion.model_validate(item) for item in record.search_result]
            except Exception:
                # 出现报错则使用新的结构返回
                search_result = [SearchQuestion2.model_validate(item) for item in record.search_result]

        solution_idea = record.solution_idea
        knowledge_points = record.knowledge_points
        solution_steps = record.solution_steps

        if solution_idea:
            solution_idea = await math_service.enrich_steps_with_messages(solution_idea)  # type: ignore

        if knowledge_points:
            knowledge_points = await math_service.enrich_steps_with_messages(knowledge_points)  # type: ignore

        if solution_steps:
            solution_steps = await math_service.enrich_steps_with_messages(solution_steps)  # type: ignore

        # 获取聊天消息记录
        chat_messages = {}
        if record.session_id:
            # 查询消息记录
            messages = await repo.get_chat_history(record.session_id, n=8)

            # 按消息类型分组
            for msg in messages:
                msg_type = getattr(msg, 'input_type', 'other')

                if msg_type not in chat_messages:
                    # 初始化该类型的数组
                    chat_messages[msg_type] = []

                # 添加消息到对应类型的数组中
                chat_messages[msg_type].append({'input': msg.input, 'output': msg.output})

        return ApiResponse(
            data=MathQuestionDetailOut(
                session_id=record.session_id,
                record_id=record.record_id,
                question=record.question,
                question_index=record.question_index,
                created_at=record.created_at.strftime('%Y年%m月%d日 %H:%M'),
                search_result=search_result,
                solution_idea=solution_idea,  # type: ignore
                knowledge_points=knowledge_points,  # type: ignore
                solution_steps=solution_steps,  # type: ignore
                chat_messages=chat_messages,  # 添加聊天消息记录
            )
        )
    except APIException:
        raise
    except Exception as e:
        logger.error(f'获取题目详情失败: {str(e)}')
        raise APIException(code=ErrorCode.SERVER_ERROR, detail=f'获取题目详情失败: {str(e)}')


@router.get('/question/detail2', summary='获取搜过题的题目详情2')
async def get_question_detail2(
    user: AuthDep,
    db: SessionDep,
    ocr_record_id: str = Query(..., description='OCR记录ID'),
    question_index: int = Query(..., description='题目在OCR中的下标'),
) -> ApiResponse[MathQuestionDetailOut2]:
    """
    根据OCR记录ID和题目索引获取题目详情

    参数:
        ocr_record_id: OCR记录ID
        question_index: 题目在OCR中的下标
        db: 数据库会话

    返回:
        题目详情
    """
    repo = MathRepository(db)

    record = await repo.get_question_detail(
        account_id=user.id,
        ocr_record_id=ocr_record_id,
        question_index=question_index,
    )

    if not record:
        raise APIException(code=ErrorCode.NOT_FOUND, detail='未找到对应的题目详情')

    try:
        # 将 JSON 数据转换为 SearchQuestionOut 对象列表
        search_result = None
        if record.search_result:
            try:
                search_result = [SearchQuestion.model_validate(item) for item in record.search_result]
            except Exception:
                # 出现报错则使用新的结构返回
                search_result = [SearchQuestion2.model_validate(item) for item in record.search_result]

        # 获取聊天消息记录
        chat_messages = defaultdict(list)
        if record_id := record.record_id:
            # 查询消息记录
            messages = await repo.get_chat_history(record_id, n=8)

            # 按消息类型分组
            for msg in messages:
                msg_type = getattr(msg, 'input_type', 'other')
                chat_messages[msg_type].append({'input': msg.input, 'output': msg.output})

        if record.explain_result:
            explain_result = ExplainDetailItem(**record.explain_result)
        else:
            explain_result = ExplainDetailItem()  # type: ignore

        data = MathQuestionDetailOut2(
            session_id=record.session_id,
            record_id=record.record_id,
            question=record.question,
            question_index=record.question_index,
            created_at=record.created_at.strftime('%Y年%m月%d日 %H:%M'),
            search_result=search_result,
            explain_result=explain_result,
            chat_messages=chat_messages,  # 添加聊天消息记录
        )

        return ApiResponse(data=data)
    except Exception as e:
        logger.error(f'获取题目详情失败: {str(e)}')
        raise APIException(code=ErrorCode.SERVER_ERROR, detail=f'获取题目详情失败: {str(e)}')


@router.post('/search/question', summary='拍照搜题')
async def search_question2(
    data: SearchQuestionIn,
    user: AuthDep,
    db: SessionDep,
) -> ApiResponse[SearchQuestionOut2]:
    """
    搜题接口，支持文本或PNG格式图片，优先用question
    """
    cfg_repo = Repository(db)
    repo = MathRepository(db)
    record_id = str(uuid.uuid4())

    if not data.question and not data.image_base64:
        return ApiResponse(code=400, msg='question和image_base64参数必须至少提供一个')

    css = await cfg_repo.get_config_by_key('hwl_search_css')
    js = await cfg_repo.get_config_by_key('hwl_search_js')
    html_template = await cfg_repo.get_appconfig_by_key('search_question')
    if html_template is None or html_template.value is None:
        return ApiResponse(code=400, msg='搜题html页面模板不存在')

    html_template = Template(html_template.value)

    search_questions: list[SearchQuestion2] = []
    hwl_result = {}
    try:
        result = await SearchQuestionService.search_question(question=data.question, image_base64=data.image_base64)
        if result and isinstance(result, dict):
            hwl_result = result
            for item in result['questionArr']:
                params = {
                    'css_urls': [*item['css'], *css],  # 多添加一项自己的文件
                    'js_urls': js,  # 不使用 好未来的js文件
                }
                question = html_template.render(html=item['question'], **params)
                explanation = html_template.render(html=item['hint'], **params)
                answer = html_template.render(html=item['answer'], **params)
                search_questions.append(
                    SearchQuestion2(
                        id=item['id'],
                        question=question,
                        explanation=explanation,
                        answer=answer,
                        similarity=item.get('rank_score', 100),
                    )
                )
            await repo.bulk_update_hwl_question(result['questionArr'])

    except TalNotQuestionException as e:
        logger.error(f'未搜索到题目: {str(e)}')
    except TalParamsException as e:
        logger.error(f'搜题参数错误: {str(e)}')
    except TalException as e:
        logger.error(f'搜题失败: {str(e)}')

    url = None
    if data.image_base64:
        # base64 转图片后保存至oss
        file, suffix = base64_to_image(data.image_base64)
        today = datetime.date.today()
        # 还需要放入用户id
        oss_path = f'/search/{today.year}/{today.month}/{today.day}'
        url = oss.upload_data(
            file,
            obj_key=record_id,
            prefix=f'math/{oss_path}',
            suffix=suffix or '.png',
        )

    # 将 Pydantic 模型转换为字典列表
    search_result_list = [q.model_dump() for q in search_questions]

    # 从搜索结果中获取相似度最高的问题
    best_question = SearchQuestionService.get_highest_similarity_question(search_result_list)
    # 先使用接口返回的文本,在找到相似度最高的问题，否则使用空字符串
    final_question = hwl_result.get('image_txt') or best_question or ''

    # 先查询是否存在记录
    instance = await repo.upsert_question_search_record(
        account_id=user.id,
        ocr_record_id=data.ocr_record_id,
        question_index=data.question_index,
        record_id=record_id,
        question=final_question,
        image_url=url,
        search_result=search_result_list,
        source_result=hwl_result,
    )
    record_id = instance.record_id

    return ApiResponse(data={'questions': search_questions, 'record_id': record_id})
