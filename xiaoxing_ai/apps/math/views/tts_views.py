import uuid

import structlog
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from langfuse.decorators import langfuse_context, observe
from pydantic import BaseModel, Field

from xiaoxing_ai.shared.api import ApiResponse
from xiaoxing_ai.shared.sse import make_sse_event

from ..deps import SessionDep
from ..repository import MathRepository
from ..services import TTSService

router = APIRouter()
logger = structlog.stdlib.get_logger('xiaoxing_ai')


class ToSpeechIn(BaseModel):
    """口语化转换请求模型"""

    text: str = Field(..., description='需要转换的文本')


@router.post('/convert', summary='数学表达式口语化转换')
@observe(capture_output=False)
async def convert_to_speech(db: SessionDep, data: ToSpeechIn):
    """数学表达式口语化转换"""

    @observe(capture_output=False)
    async def speech_generator(db_session: SessionDep):
        math_repo = MathRepository(db_session)
        try:
            # 生成唯一的会话ID
            thread_id = str(uuid.uuid4())
            langfuse_context.update_current_trace(session_id=thread_id)
            # 用于累积完整的转换结果
            buffer = []

            # 调用口语化转换服务，获取流式结果
            async for result in TTSService(math_repo, thread_id).speechify(data.text):
                if result:
                    buffer.append(result)
                    yield make_sse_event(
                        {'thread_id': thread_id, 'content': result, 'role': 'assistant'}, event_type='tts'
                    )

            if buffer:
                langfuse_context.update_current_observation(output=''.join(buffer))

            # 发送完成事件，包含完整的转换结果
            yield make_sse_event(
                {'thread_id': thread_id, 'role': 'assistant', 'finish_reason': 'stop'}, event_type='tts'
            )
        except Exception as e:
            logger.error(f'生成口语化转换出错: {str(e)}')
            raise HTTPException(status_code=400, detail='生成错误') from e
        finally:
            await math_repo.db.aclose()  # 主动close

    try:
        return StreamingResponse(speech_generator(db), media_type='text/event-stream')
    except Exception as e:
        logger.error(f'口语化转换失败: {str(e)}')
        return ApiResponse(code=500, msg=f'口语化转换失败: {str(e)}')
