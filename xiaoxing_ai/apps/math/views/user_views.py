from fastapi import APIRouter
from fastapi.responses import HTMLResponse

from xiaoxing_ai.shared.api import APIException, ApiResponse, ErrorCode

from ..constants import NOT_FOUND_PAGE
from ..deps import AuthDep, SessionDep, YYPXSessionDep
from ..dto.user import CheckVersionOut, UpdateUserInfo, UserInfoOut
from ..repository import Repository, YYPXRepository

router = APIRouter()


@router.get('/users/info', summary='获取用户信息')
async def user_info(user: AuthDep, db: YYPXSessionDep) -> ApiResponse[UserInfoOut]:
    info = await YYPXRepository(db).get_user_info(user.user_id)
    if info is None:
        raise APIException(code=ErrorCode.NOT_FOUND, detail='未查询到对应的用户信息')

    return ApiResponse(data={'id': user.id, 'account': user.account, **info})


@router.put('/users/info', summary='更新用户信息')
async def update_user_info(data: UpdateUserInfo, user: AuthDep, db: YYPXSessionDep) -> ApiResponse[UserInfoOut]:
    info = await YYPXRepository(db).get_user(user.user_id)
    if info is None:
        raise APIException(code=ErrorCode.NOT_FOUND, detail='未查询到对应的用户信息')

    info.grade_id = data.grade_id

    await db.commit()

    return ApiResponse()


@router.get('/users/privacy_policy', summary='获取隐私协议')
async def privacy_policy(db: SessionDep) -> HTMLResponse:
    """隐私协议"""
    data = await Repository(db).get_appconfig_by_key('privacy_policy')
    if data is None:
        return HTMLResponse(NOT_FOUND_PAGE)

    return HTMLResponse(data.value)


@router.get('/users/user_agreement', summary='获取用户协议')
async def user_agreement(db: SessionDep) -> HTMLResponse:
    """用户协议"""
    data = await Repository(db).get_appconfig_by_key('user_agreement')
    if data is None:
        return HTMLResponse(NOT_FOUND_PAGE)

    return HTMLResponse(data.value)


@router.get('/users/about_us', summary='获取关于我们')
async def about_us(db: SessionDep) -> HTMLResponse:
    """关于我们"""
    data = await Repository(db).get_appconfig_by_key('about_us')
    if data is None:
        return HTMLResponse(NOT_FOUND_PAGE)

    return HTMLResponse(data.value)


@router.get('/check_version', summary='检查应用版本')
async def check_version(db: SessionDep) -> ApiResponse[CheckVersionOut]:
    data = await Repository(db).get_max_version()

    if data is None:
        raise APIException(code=ErrorCode.NOT_FOUND, detail='未查询到数据')

    return ApiResponse(data=dict(data))
