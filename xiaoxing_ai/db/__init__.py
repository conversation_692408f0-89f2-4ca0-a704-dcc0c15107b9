from typing import As<PERSON><PERSON><PERSON><PERSON>

import structlog
from redis.asyncio import StrictRed<PERSON>
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from xiaoxing_ai.settings import settings

logger = structlog.stdlib.get_logger('xiaoxing_ai.db')

redis_client = StrictRedis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    username=settings.REDIS_USER,
    password=settings.REDIS_PASSWORD or None,
    db=settings.REDIS_DB,
    decode_responses=True,
    health_check_interval=15,
    socket_keepalive=True,
    retry_on_timeout=True,
)

async_engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,
    pool_pre_ping=True,
    pool_size=20,
    max_overflow=100,
    pool_recycle=3600,
)
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autoflush=False,
    expire_on_commit=False,
)

gz_async_engine = create_async_engine(
    settings.GZ_DATABASE_URL,
    echo=False,
    pool_pre_ping=True,
    pool_size=20,
    max_overflow=50,
    pool_recycle=3600,  # 每小时回收空闲连接
)
GzAsyncSessionLocal = async_sessionmaker(
    bind=gz_async_engine,
    class_=AsyncSession,
    autoflush=False,
    expire_on_commit=False,
)

yypx_async_engine = create_async_engine(
    settings.YYPX_DATABASE_URL,
    echo=False,
    pool_pre_ping=True,
    pool_size=20,
    max_overflow=100,
    pool_recycle=3600,
)
YYPXAsyncSessionLocal = async_sessionmaker(
    bind=yypx_async_engine,
    class_=AsyncSession,
    autoflush=False,
    expire_on_commit=False,
)


# DB Session for Dependency Injection
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def check_connection(session: AsyncSession) -> bool:
    """检查数据库连接是否有效"""
    try:
        # 执行一个简单的查询来检查连接是否有效
        await session.execute(text('SELECT 1'))
        return True
    except Exception as e:
        logger.error('Database connection is invalid. Reconnecting...', exc_info=e)
        return False


# DB Session for Dependency Injection
async def get_gz_db() -> AsyncGenerator[AsyncSession, None]:
    async with GzAsyncSessionLocal() as session:
        if not await check_connection(session):
            await session.close()
            async with GzAsyncSessionLocal() as new_session:
                session = new_session

        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_yypx_db() -> AsyncGenerator[AsyncSession, None]:
    async with YYPXAsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
