"""
腾讯云 爱作文 的数据库表
"""

from sqlalchemy import Integer
from sqlalchemy.dialects.mysql import TEXT, VARCHAR
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    pass


class QaReference(Base):
    __tablename__ = 'ac_qa_reference'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    title: Mapped[str] = mapped_column(VARCHAR(255))
    question: Mapped[str] = mapped_column(VARCHAR(255))
    answer: Mapped[str] = mapped_column(TEXT)
