import datetime
import decimal
from typing import Optional

from sqlalchemy import DECIMAL, TIMESTAMP, BigInteger, Date, DateTime, Index, Integer, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYINT, VARCHAR
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    pass


class AuthAccount(Base):
    __tablename__ = 'auth_account'
    __table_args__ = (
        Index('account', 'account'),
        Index('company_id', 'company_id'),
        Index('email', 'email'),
        Index('mobile', 'mobile'),
        Index('qq_code', 'qq_code'),
        Index('qq_code_2', 'wechat_code'),
    )

    id: Mapped[int] = mapped_column(BIGINT, primary_key=True)
    type: Mapped[Optional[int]] = mapped_column(
        TINYINT, server_default=text("'0'"), comment='用户类型 1:平台管理员 2:用户 3: 经销商  4：教练'
    )
    user_id: Mapped[Optional[int]] = mapped_column(BIGINT, server_default=text("'0'"), comment='用户id')
    company_id: Mapped[Optional[int]] = mapped_column(BIGINT, server_default=text("'0'"))
    account: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='普通账号')
    mobile: Mapped[Optional[str]] = mapped_column(VARCHAR(12), server_default=text("''"), comment='手机号码')
    email: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='邮箱地址')
    password: Mapped[Optional[str]] = mapped_column(VARCHAR(255), server_default=text("''"))
    qq_code: Mapped[Optional[str]] = mapped_column(VARCHAR(255), server_default=text("''"), comment='绑定的qq')
    wechat_code: Mapped[Optional[str]] = mapped_column(VARCHAR(255), server_default=text("''"), comment='绑定的微信')
    wechat_openid: Mapped[Optional[str]] = mapped_column(VARCHAR(100), server_default=text("''"), comment='绑定的微信')
    status: Mapped[Optional[int]] = mapped_column(TINYINT, server_default=text("'0'"), comment='状态  1：启用  0：禁用')
    wechat_verify: Mapped[Optional[int]] = mapped_column(
        TINYINT, server_default=text("'1'"), comment='微信扫码验证 0：禁用  1：启用'
    )
    reset_password_number: Mapped[Optional[int]] = mapped_column(
        INTEGER, server_default=text("'0'"), comment='密码设置次数'
    )
    data_scope: Mapped[Optional[int]] = mapped_column(
        TINYINT, server_default=text("'0'"), comment='数据权限范围 1：正常用户  2：测试用户  3：运营'
    )
    param: Mapped[Optional[str]] = mapped_column(
        VARCHAR(255), server_default=text("''"), comment='额外的参数字段 json 格式'
    )
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='修改时间')
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='最后一次修改时间')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='删除时间')


class Grade(Base):
    __tablename__ = 'grade'

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    name: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='名称')
    sort: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='排序号')
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class User(Base):
    __tablename__ = 'user'
    __table_args__ = (
        Index('agent_id', 'agent_id'),
        Index('grade_id', 'grade_id'),
        Index('mobile', 'mobile'),
        Index('teacher_id', 'teacher_id'),
    )

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    auth_account_id: Mapped[Optional[int]] = mapped_column(BigInteger, server_default=text("'0'"), comment='关联账号')
    agent_id: Mapped[Optional[int]] = mapped_column(BigInteger, server_default=text("'0'"), comment='所属经销商')
    teacher_id: Mapped[Optional[int]] = mapped_column(BigInteger, server_default=text("'0'"), comment='推荐教练')
    name: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='姓名')
    portrait: Mapped[Optional[str]] = mapped_column(VARCHAR(255), server_default=text("''"), comment='头像')
    birthday: Mapped[Optional[datetime.date]] = mapped_column(Date, comment='生日')
    age: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='年龄')
    gender: Mapped[Optional[int]] = mapped_column(TINYINT, server_default=text("'1'"), comment='性别  1：男   2：女')
    school: Mapped[Optional[str]] = mapped_column(VARCHAR(50), comment='学校')
    grade_id: Mapped[Optional[int]] = mapped_column(BigInteger, server_default=text("'0'"), comment='年级')
    mobile: Mapped[Optional[str]] = mapped_column(VARCHAR(11), server_default=text("''"), comment='手机号')
    father_work: Mapped[Optional[str]] = mapped_column(VARCHAR(100), server_default=text("''"), comment='父亲职业')
    mother_work: Mapped[Optional[str]] = mapped_column(VARCHAR(100), comment='母亲职业')
    address: Mapped[Optional[str]] = mapped_column(VARCHAR(255), comment='家庭住址')
    chinese_score: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='语文成绩')
    math_score: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='数学成绩')
    english_score: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='英语成绩')
    class_rank: Mapped[Optional[int]] = mapped_column(Integer, comment='班级排名')
    grade_rank: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='年级排名')
    extra_study: Mapped[Optional[int]] = mapped_column(
        TINYINT, server_default=text("'0'"), comment='补习情况   1：曾经补习  2：正在补习  3：从未补习'
    )
    learning_interest: Mapped[Optional[int]] = mapped_column(
        TINYINT, server_default=text("'0'"), comment='学习兴趣  1：有  2：无  3：一般'
    )
    source: Mapped[Optional[int]] = mapped_column(
        TINYINT, server_default=text("'0'"), comment='生源信息   1：招生信息   2：朋友推荐  3：转介绍   4：其他'
    )
    referrer_code: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='推荐教练编号')
    recommend_code: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='推荐码')
    recommender: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='推荐人')
    vocabulary: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='首测词汇量')
    english_level: Mapped[Optional[str]] = mapped_column(VARCHAR(50), server_default=text("''"), comment='英语水平')
    trial_teacher_id: Mapped[Optional[int]] = mapped_column(BigInteger, server_default=text("'0'"), comment='体验教练')
    trial_course_id: Mapped[Optional[int]] = mapped_column(BigInteger, server_default=text("'0'"), comment='体验词库')
    trial_date: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='体验日期')
    trial_duration: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='体验时长')
    remember_words: Mapped[Optional[int]] = mapped_column(Integer, server_default=text("'0'"), comment='记住词汇')
    accuracy_rate: Mapped[Optional[decimal.Decimal]] = mapped_column(
        DECIMAL(10, 2), server_default=text("'0.00'"), comment='正确率'
    )
    status: Mapped[Optional[int]] = mapped_column(TINYINT, server_default=text("'0'"), comment='状态  0：禁用  1：启用')
    remark: Mapped[Optional[str]] = mapped_column(VARCHAR(500), server_default=text("''"), comment='备注')
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)
