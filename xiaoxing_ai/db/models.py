import datetime
from typing import Optional

from sqlalchemy import <PERSON>SO<PERSON>, TIMESTAMP, BigInteger, DateTime, Float, Index, Integer, String, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, TEXT, TINYINT, VARCHAR
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    pass


class AiApp(Base):
    __tablename__ = 'ai_app'
    __table_args__ = (
        Index('idx_ai_model_key', 'ai_model_key'),
        Index('pk_app_module', 'app_module', unique=True),
        {'comment': '业务实例表'},
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    app_module: Mapped[str] = mapped_column(String(50), server_default=text("''"), comment='业务模块')
    ai_model_key: Mapped[str] = mapped_column(String(50), server_default=text("''"), comment='关联的ai模型实例key')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    params: Mapped[Optional[dict]] = mapped_column(JSON, comment='模型参数，覆盖ai模型默认参数')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='记录删除时间(软删除)')


class AiInstance(Base):
    __tablename__ = 'ai_instance'
    __table_args__ = (Index('idx_key', 'key'), {'comment': 'ai模型实例表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    key: Mapped[str] = mapped_column(
        String(50),
        server_default=text("''"),
        comment='模型key 例如：openai:gpt-4o, deepseek:deepseek-chat, azure:gpt-4o-mini',
    )
    api_key: Mapped[str] = mapped_column(String(255), comment='api秘钥')
    base_url: Mapped[str] = mapped_column(String(100), server_default=text("''"), comment='url')
    is_active: Mapped[int] = mapped_column(TINYINT, server_default=text("'0'"), comment='是否启用(0: 禁用 1: 启用)')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    params: Mapped[Optional[dict]] = mapped_column(
        JSON, comment='拓展字段，例如: {"temperature": 0.7, "stream": true...}'
    )
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='记录删除时间(软删除)')


class AiPrompt(Base):
    __tablename__ = 'ai_prompt'
    __table_args__ = (
        Index('idx_app_name', 'app_name'),
        Index('uk_pid', 'pid', unique=True),
        {'comment': 'AI提示词模板表'},
    )

    id: Mapped[int] = mapped_column(INTEGER, primary_key=True, comment='自增主键')
    app_name: Mapped[str] = mapped_column(
        String(20, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='app名称'
    )
    pid: Mapped[str] = mapped_column(String(36, 'utf8mb4_unicode_ci'), comment='提示词UUID，业务唯一标识')
    name: Mapped[str] = mapped_column(String(50, 'utf8mb4_unicode_ci'), comment='模板名称')
    description: Mapped[str] = mapped_column(Text(collation='utf8mb4_unicode_ci'), comment='详细描述')
    prompt: Mapped[str] = mapped_column(Text(collation='utf8mb4_unicode_ci'), comment='提示词内容')
    category: Mapped[str] = mapped_column(String(50, 'utf8mb4_unicode_ci'), comment='提示词分类（文本、图像、视频）')
    created_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='记录创建时间'
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='记录更新时间'
    )
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='记录删除时间(软删除)')


class AppConfig(Base):
    __tablename__ = 'app_config'
    __table_args__ = (Index('u_app_name_key_name', 'app_name', 'key_name', unique=True), {'comment': 'app配置表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='自增id')
    app_name: Mapped[str] = mapped_column(String(50), comment='app名称')
    name: Mapped[str] = mapped_column(String(50), comment='配置名称')
    key_name: Mapped[str] = mapped_column(String(50), comment='键')
    created_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间'
    )
    value: Mapped[Optional[str]] = mapped_column(LONGTEXT, comment='值')
    description: Mapped[Optional[str]] = mapped_column(String(255), comment='描述')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='删除时间')


class AppVersion(Base):
    __tablename__ = 'app_version'
    __table_args__ = (Index('u_version_md5', 'version_code', 'md5', unique=True), {'comment': 'app版本表'})

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    version_name: Mapped[str] = mapped_column(String(50), comment='版本名称（如：1.0.0_013110）')
    version_code: Mapped[int] = mapped_column(Integer, comment='第几个版本（如：10）')
    url: Mapped[str] = mapped_column(Text, comment='下载链接')
    created_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间'
    )
    package_name: Mapped[Optional[str]] = mapped_column(String(255), comment='包名')
    description: Mapped[Optional[str]] = mapped_column(Text, comment='版本发布说明')
    file_size: Mapped[Optional[int]] = mapped_column(Integer, comment='应用文件大小（单位：字节）')
    icon: Mapped[Optional[str]] = mapped_column(Text, comment='图标链接或base64内容')
    is_forced: Mapped[Optional[int]] = mapped_column(TINYINT, server_default=text("'0'"), comment='是否强制下载')
    md5: Mapped[Optional[str]] = mapped_column(Text, comment='文件MD5码')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='删除时间')


class Article(Base):
    __tablename__ = 'article'
    __table_args__ = (Index('idx_uid', 'uid'), {'comment': '作文评分表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(String(36))
    level: Mapped[str] = mapped_column(VARCHAR(255), comment='等级')
    topic: Mapped[str] = mapped_column(TEXT, comment='标题')
    content: Mapped[str] = mapped_column(Text, comment='内容')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    result: Mapped[Optional[dict]] = mapped_column(JSON, comment='结果')
    improve_result: Mapped[Optional[str]] = mapped_column(Text, comment='优化后的作文结果')
    extra: Mapped[Optional[dict]] = mapped_column(JSON, comment='额外字段')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class ArticleHomeworkImprovedRecords(Base):
    __tablename__ = 'article_homework_improved_records'
    __table_args__ = (Index('idx_uid', 'uid'), {'comment': '作业优化记录表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(String(36))
    ahr_uid: Mapped[str] = mapped_column(String(36), comment='作业记录表uid')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    content: Mapped[Optional[str]] = mapped_column(Text, comment='优化后的作文')
    result: Mapped[Optional[dict]] = mapped_column(JSON, comment='优化作文的评价结果')
    extra: Mapped[Optional[dict]] = mapped_column(JSON, comment='额外的')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class ArticleHomeworkRecords(Base):
    __tablename__ = 'article_homework_records'
    __table_args__ = (Index('idx_uid', 'uid'), {'comment': '作业评价表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(String(36))
    params: Mapped[dict] = mapped_column(JSON, comment='请求参数')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    result: Mapped[Optional[dict]] = mapped_column(JSON, comment='结果')
    extra: Mapped[Optional[dict]] = mapped_column(JSON, comment='额外的')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class ArticleImproved(Base):
    __tablename__ = 'article_improved'
    __table_args__ = (Index('idx_uid', 'a_uid'), {'comment': '作文优化记录表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(VARCHAR(36))
    a_uid: Mapped[str] = mapped_column(VARCHAR(36), comment='article的uuid')
    source_content: Mapped[str] = mapped_column(TEXT, comment='原始作文')
    content: Mapped[str] = mapped_column(TEXT, comment='优化后的作文')
    result_task_status: Mapped[int] = mapped_column(
        Integer, server_default=text("'0'"), comment='评分结果任务状态 0-待处理 1-处理中 2-成功 3-失败'
    )
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    summary: Mapped[Optional[str]] = mapped_column(TEXT, comment='总结内容')
    result: Mapped[Optional[dict]] = mapped_column(JSON, comment='优化后作文的评分结果')
    extra: Mapped[Optional[dict]] = mapped_column(JSON, comment='额外字段')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class ChatMessage(Base):
    __tablename__ = 'chat_message'
    __table_args__ = (
        Index('idx_session_id', 'session_id'),
        {
            'comment': '问题类型 math: understanding_agent(解题思路) knowledge_point_agent(知识点) '
            'solution_steps_agent(解答过程)'
        },
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    session_id: Mapped[str] = mapped_column(String(36, 'utf8mb4_unicode_ci'), comment='会话ID')
    input: Mapped[str] = mapped_column(TEXT, comment='用户输入')
    input_type: Mapped[str] = mapped_column(
        String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='问题类型'
    )
    output: Mapped[str] = mapped_column(Text(collation='utf8mb4_unicode_ci'), comment='模型回答')
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'
    )
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间'
    )
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='删除时间')


class ChatSession(Base):
    __tablename__ = 'chat_session'
    __table_args__ = (Index('idx_session_id', 'session_id'), Index('uk_session_id', 'session_id', unique=True))

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    session_id: Mapped[str] = mapped_column(String(36, 'utf8mb4_unicode_ci'), comment='会话ID')
    user_id: Mapped[str] = mapped_column(
        String(36, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='用户唯一标识（预留）'
    )
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'
    )
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间'
    )
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='删除时间')


class HwlQuestionLog(Base):
    __tablename__ = 'hwl_question_log'
    __table_args__ = (Index('u_uid', 'uid', unique=True), {'comment': '好未来题目记录表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(String(255), comment='搜题服务的请求uid')
    question: Mapped[str] = mapped_column(LONGTEXT, comment='题目html')
    hint: Mapped[str] = mapped_column(LONGTEXT, comment='解析html')
    answer: Mapped[str] = mapped_column(LONGTEXT, comment='答案html')
    js: Mapped[dict] = mapped_column(JSON, comment='渲染html的js文件')
    css: Mapped[dict] = mapped_column(JSON, comment='渲染html的css文件')
    rank_score: Mapped[float] = mapped_column(Float, comment='相似度得分')
    source: Mapped[int] = mapped_column(Integer, server_default=text("'0'"), comment='源题id')
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)


class Interview(Base):
    __tablename__ = 'interview'
    __table_args__ = (Index('idx_uid', 'uid'),)

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(VARCHAR(36))
    question_id: Mapped[int] = mapped_column(Integer, comment='面试题目id')
    student_answer: Mapped[str] = mapped_column(TEXT, comment='考生回答')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    evaluation: Mapped[Optional[str]] = mapped_column(TEXT, comment='评价结果')
    improved_answer: Mapped[Optional[str]] = mapped_column(TEXT, comment='改进后的答案')
    improved_evaluation: Mapped[Optional[str]] = mapped_column(TEXT, comment='改进后答案的评价')
    extra: Mapped[Optional[dict]] = mapped_column(JSON, comment='额外字段')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class InterviewImprovedRecords(Base):
    __tablename__ = 'interview_improved_records'
    __table_args__ = (Index('idx_uid', 'uid'), {'comment': '公务员-面试-优化记录表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(VARCHAR(36), comment='优化id')
    question: Mapped[str] = mapped_column(TEXT, comment='面试题目')
    answer: Mapped[str] = mapped_column(TEXT, comment='用户答案')
    evaluation: Mapped[str] = mapped_column(TEXT, comment='用户答案评价')
    improved_answer: Mapped[str] = mapped_column(Text, comment='优化答案')
    improved_evaluation: Mapped[str] = mapped_column(Text, comment='优化答案的评价')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    extra: Mapped[Optional[dict]] = mapped_column(JSON, comment='额外字段')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class InterviewQuestion(Base):
    __tablename__ = 'interview_question'
    __table_args__ = (Index('idx_question', 'question'), Index('idx_uid', 'uid'), {'comment': '公务员-面试-参考答案表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(VARCHAR(36))
    question: Mapped[str] = mapped_column(TEXT, comment='面试题目')
    type: Mapped[str] = mapped_column(TEXT, comment='面试题类型')
    standard_answer: Mapped[str] = mapped_column(TEXT, comment='参考答案')
    evaluation: Mapped[str] = mapped_column(TEXT, comment='参考答案的评价')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class InterviewRecords(Base):
    __tablename__ = 'interview_records'
    __table_args__ = (Index('idx_uid', 'uid'), {'comment': '公务员-面试-评价记录表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(VARCHAR(36))
    question: Mapped[str] = mapped_column(Text, comment='问题')
    type: Mapped[str] = mapped_column(String(255), comment='问题类型')
    answer: Mapped[str] = mapped_column(TEXT, comment='考生回答')
    created_at: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    )
    evaluation: Mapped[Optional[str]] = mapped_column(TEXT, comment='评价结果')
    extra: Mapped[Optional[dict]] = mapped_column(JSON, comment='额外字段')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)


class MathOcrRecords(Base):
    __tablename__ = 'math_ocr_records'
    __table_args__ = (Index('idx_record_id', 'record_id'), {'comment': '数学-ocr识别记录表'})

    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键')
    account_id: Mapped[int] = mapped_column(Integer, server_default=text("'0'"), comment='账号id')
    record_id: Mapped[str] = mapped_column(String(36), server_default=text("''"), comment='记录唯一标识')
    question_type: Mapped[str] = mapped_column(VARCHAR(20), comment='题目类型;')
    is_active: Mapped[int] = mapped_column(TINYINT, server_default=text("'0'"), comment='数据是否使用')
    image_url: Mapped[str] = mapped_column(Text, comment='题目图片URL')
    ocr_result: Mapped[dict] = mapped_column(JSON, comment='OCR识别结果')
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间'
    )
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='删除时间')


class MathQuestionSearchRecords(Base):
    __tablename__ = 'math_question_search_records'
    __table_args__ = (
        Index('idx_ocr_record_id', 'ocr_record_id'),
        Index('idx_record_id', 'record_id'),
        Index('idx_session_id', 'session_id'),
        {'comment': '数学-搜题记录表'},
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键')
    account_id: Mapped[int] = mapped_column(Integer, server_default=text("'0'"), comment='账号id')
    record_id: Mapped[str] = mapped_column(String(36), server_default=text("''"), comment='记录唯一标识')
    ocr_record_id: Mapped[str] = mapped_column(VARCHAR(36), comment='关联的OCR记录ID')
    question: Mapped[str] = mapped_column(LONGTEXT, comment='题目文本')
    question_index: Mapped[int] = mapped_column(Integer, comment='题目文本在OCR中的下标')
    source_result: Mapped[dict] = mapped_column(JSON, comment='原始搜题结果')
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间'
    )
    session_id: Mapped[Optional[str]] = mapped_column(VARCHAR(36), comment='会话标识')
    image_url: Mapped[Optional[str]] = mapped_column(String(255), comment='题目图片url')
    search_result: Mapped[Optional[dict]] = mapped_column(JSON, comment='搜题结果')
    solution_idea: Mapped[Optional[dict]] = mapped_column(JSON, comment='解题思路')
    knowledge_points: Mapped[Optional[dict]] = mapped_column(JSON, comment='关联知识点')
    solution_steps: Mapped[Optional[dict]] = mapped_column(JSON, comment='解答过程')
    problem: Mapped[Optional[str]] = mapped_column(TEXT, comment='AI讲题用户输入的问题')
    deleted_at: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='删除时间')
    explain_result: Mapped[Optional[dict]] = mapped_column(JSON, comment='ai流式讲解回答')
