import uuid
from typing import Sequence

from jinja2 import Template
from sqlalchemy import select
from sqlalchemy.ext.asyncio.session import AsyncSession

from xiaoxing_ai.db.models import AiApp, AiInstance, AiPrompt, ChatMessage, ChatSession
from xiaoxing_ai.shared.llms import LLMConfig, LLMParams


class CommonRepository:
    """公共查询类"""

    app_name: str  # 类属性

    def __init__(self, db: AsyncSession) -> None:
        self.db = db

    async def get_or_create_session(self, session_id: uuid.UUID | None = None) -> str | None:
        """获取或创建会话"""
        if not session_id:
            new_session_id = uuid.uuid4()
            instance = ChatSession(session_id=str(new_session_id))
            self.db.add(instance)
            await self.db.flush()
            await self.db.commit()

            return instance.session_id

        # session exists or not
        q = await self.db.execute(select(ChatSession).where(ChatSession.session_id == str(session_id)))
        data = q.scalars().first()
        if not data:
            return None

        return data.session_id

    async def get_chat_history(self, session_id: uuid.UUID | str, n: int) -> Sequence[ChatMessage]:
        """获取对话记录"""
        stmt = (
            select(ChatMessage)
            .where(ChatMessage.session_id == str(session_id))
            .order_by(ChatMessage.created_at.desc())
            .limit(n)
        )

        q = await self.db.execute(stmt)
        data = q.scalars().all()

        # 按创建时间重新排序
        return data[::-1]

    async def create_message(self, session_id: uuid.UUID | str, input: str, content: str, input_type: str = ''):
        """创建新消息记录"""
        instance = ChatMessage(session_id=str(session_id), input_type=input_type, input=input, output=content)
        self.db.add(instance)
        await self.db.commit()

    async def get_ai_config_by_module(self, app_module: str) -> LLMConfig | None:
        """根据业务获取模型实例"""
        stmt = (
            select(
                AiInstance.key,
                AiInstance.api_key,
                AiInstance.base_url,
                AiInstance.params.label('default_params'),
                AiApp.params,
            )
            .select_from(AiApp)
            .join(AiInstance, AiApp.ai_model_key == AiInstance.key)
            .where(
                AiApp.app_module == app_module,
                AiApp.deleted_at.is_(None),
                AiInstance.is_active == 1,  # 启用
                AiInstance.deleted_at.is_(None),
            )
        )

        q = await self.db.execute(stmt)
        data = q.mappings().first()
        if not data:
            return None

        params = data.get('default_params', {}) | data.get('params', {})
        instance_key = data.get('key', '')
        # 从key中拆分服务商和模型，格式为 provider:model
        parts = instance_key.split(':')
        if len(parts) < 2:
            raise ValueError('invalid provider_key, the format should be provider:model')

        return LLMConfig(
            key=data['key'],
            provider=parts[0],
            model=parts[1],
            api_key=data['api_key'],
            base_url=data['base_url'],
            timeout=params.get('timeout', 60),
            api_version=params.get('api_version', ''),
            params=LLMParams(
                stream=params.get('stream', False),
                temperature=params.get('temperature', 0.7),
                tools=params.get('tools'),
            ),
        )

    async def get_model_prompt(self, prompt_name: str) -> Template | None:
        """获取模型提示词"""

        stmt = select(AiPrompt.prompt).where(
            AiPrompt.app_name == self.app_name,
            AiPrompt.name == prompt_name,
            AiPrompt.deleted_at.is_(None),
        )

        q = await self.db.execute(stmt)
        content = q.scalars().first()
        if not content:
            return None
        return Template(content)
