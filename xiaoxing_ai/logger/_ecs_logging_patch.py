# ruff: noqa
# This file is a patch for `ecs_logging` package.
# The changes are:
# - Add a new method `json_dumps` to replace the original `ecs_logging._utils.json_dumps` method to disable `ensure_ascii` for chinese content.
# - Add a new method `format_to_ecs` to replace the original `ecs_logging._utils.json_dumps` method to fix `@timestamp` without timezone.

import datetime
import functools
import json

from ecs_logging._meta import ECS_VERSION
from ecs_logging._utils import _json_dumps_fallback, normalize_dict

from typing import Any, Dict


# Original version: ecs_logging._utils.json_dumps
# Patched version
def json_dumps(value):
    # type: (Dict[str, Any]) -> str

    # Ensure that the first three fields are '@timestamp',
    # 'log.level', and 'message' per ECS spec
    ordered_fields = []
    try:
        ordered_fields.append(('@timestamp', value.pop('@timestamp')))
    except KeyError:
        pass

    # log.level can either be nested or not nested so we have to try both
    try:
        ordered_fields.append(('log.level', value['log'].pop('level')))
        if not value['log']:  # Remove the 'log' dictionary if it's now empty
            value.pop('log', None)
    except KeyError:
        try:
            ordered_fields.append(('log.level', value.pop('log.level')))
        except KeyError:
            pass
    try:
        ordered_fields.append(('message', value.pop('message')))
    except KeyError:
        pass

    json_dumps = functools.partial(
        json.dumps,
        sort_keys=True,
        separators=(',', ':'),
        # disable `ensure_ascii` for chinese content.
        ensure_ascii=False,
        default=_json_dumps_fallback,
    )

    # Because we want to use 'sorted_keys=True' we manually build
    # the first three keys and then build the rest with json.dumps()
    if ordered_fields:
        # Need to call json.dumps() on values just in
        # case the given values aren't strings (even though
        # they should be according to the spec)
        ordered_json = ','.join(f'"{k}":{json_dumps(v)}' for k, v in ordered_fields)
        if value:
            return '{{{},{}'.format(
                ordered_json,
                json_dumps(value)[1:],
            )
        else:
            return '{%s}' % ordered_json
    # If there are no fields with ordering requirements we
    # pass everything into json.dumps()
    else:
        return json_dumps(value)


class PatchedStructlogFormatter:
    """ECS formatter for the ``structlog`` module"""

    def __call__(self, _, name, event_dict):
        # type: (Any, str, Dict[str, Any]) -> str

        # Handle event -> message now so that stuff like `event.dataset` doesn't
        # cause problems down the line
        event_dict['message'] = str(event_dict.pop('event'))
        event_dict = normalize_dict(event_dict)
        event_dict.setdefault('log', {}).setdefault('level', name.lower())
        event_dict = self.format_to_ecs(event_dict)
        return json_dumps(event_dict)

    def format_to_ecs(self, event_dict):
        # type: (Dict[str, Any]) -> Dict[str, Any]
        if '@timestamp' not in event_dict:
            event_dict['@timestamp'] = (
                datetime.datetime.now(datetime.timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z',
            )

        if 'exception' in event_dict:
            stack_trace = event_dict.pop('exception')
            if 'error' in event_dict:
                event_dict['error']['stack_trace'] = stack_trace
            else:
                event_dict['error'] = {'stack_trace': stack_trace}

        event_dict.setdefault('ecs', {}).setdefault('version', ECS_VERSION)
        return event_dict
