import datetime
import logging
import logging.config

import structlog
from elasticsearch import Elasticsearch
from opentelemetry import trace
from structlog.processors import CallsiteParameter

from xiaoxing_ai.settings import settings

from ._ecs_logging_patch import PatchedStructlogFormatter

logger = structlog.get_logger('xiaoxing_ai')

es_client = Elasticsearch(hosts=[settings.ES_BASE_URL])


def add_open_telemetry_spans(_, __, event_dict):
    span = trace.get_current_span()
    if not span.is_recording():
        event_dict['span'] = {}
        return event_dict

    ctx = span.get_span_context()
    parent = getattr(span, 'parent', None)

    event_dict['span'] = {
        'span_id': hex(ctx.span_id),
        'trace_id': hex(ctx.trace_id),
        'parent_span_id': hex(0x0) if not parent else hex(parent.span_id),
    }

    return event_dict


class ElasticsearchHandler(logging.Handler):
    def emit(self, record):
        current_date = datetime.datetime.now().strftime('%Y-%m-%d')
        index_name = f'{settings.ES_INDEX_PREFIX}-{current_date}'
        log_entry = self.format(record)
        try:
            es_client.index(index=index_name, body=log_entry)  # type: ignore
        except Exception as e:
            logger.exception('Failed to log to Elasticsearch', exc_info=e)


pre_chains = [
    structlog.contextvars.merge_contextvars,
    structlog.processors.TimeStamper(fmt='%Y-%m-%d %H:%M:%S', utc=False),
    structlog.processors.add_log_level,
    add_open_telemetry_spans,
    structlog.stdlib.add_logger_name,
    structlog.stdlib.PositionalArgumentsFormatter(),
    structlog.processors.CallsiteParameterAdder(
        [
            CallsiteParameter.PATHNAME,
            CallsiteParameter.MODULE,
            CallsiteParameter.LINENO,
            CallsiteParameter.FUNC_NAME,
        ]
    ),
    structlog.stdlib.ExtraAdder(),
    structlog.processors.StackInfoRenderer(),
    structlog.processors.format_exc_info,
]


def setup_logging(enable_json: bool = True, log_level: str = 'INFO'):
    handlers = ['default']
    if enable_json:
        handlers.append('es')

    # Configure for structlog
    structlog.configure(
        context_class=dict,
        processors=[
            *pre_chains,
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure for standard logging library
    logging.config.dictConfig(
        {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'console': {
                    '()': structlog.stdlib.ProcessorFormatter,
                    'processors': [
                        structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                        structlog.dev.ConsoleRenderer(colors=True),
                    ],
                    'foreign_pre_chain': pre_chains,
                },
                'json': {
                    '()': structlog.stdlib.ProcessorFormatter,
                    'processors': [
                        structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                        # 这里不需要 JSONRenderer(), 转为文本, 因为原本就是dict
                        PatchedStructlogFormatter(),
                    ],
                    'foreign_pre_chain': pre_chains,
                },
            },
            'handlers': {
                'default': {
                    'level': log_level,
                    'class': 'logging.StreamHandler',
                    'formatter': 'console',
                },
                'es': {
                    'level': log_level,
                    'class': 'xiaoxing_ai.logger.ElasticsearchHandler',
                    'formatter': 'json',
                },
            },
            'loggers': {
                'xiaoxing_ai': {
                    'handlers': handlers,
                    'level': log_level,
                    'propagate': False,
                },
                'fastapi': {
                    'handlers': handlers,
                    'level': log_level,
                    'propagate': False,
                },
                'uvicorn': {
                    'handlers': handlers,
                    'level': log_level,
                    'propagate': False,
                },
                'httpx': {
                    'handlers': handlers,
                    'level': log_level,
                    'propagate': False,
                },
                'openai': {
                    'handlers': handlers,
                    'level': log_level,
                    'propagate': False,
                },
                'langfuse': {
                    'handlers': handlers,
                    'level': log_level,
                    'propagate': False,
                },
            },
        }
    )
