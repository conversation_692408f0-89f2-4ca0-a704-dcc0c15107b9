import secrets
import time
from contextlib import asynccontextmanager

from fastapi import Depends, FastAPI, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from langfuse.decorators import langfuse_context
from opentelemetry.instrumentation.asyncio import Async<PERSON><PERSON>nstrumentor
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.openai import OpenAIInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.cors import CORSMiddleware

from xiaoxing_ai.db import async_engine, gz_async_engine, yypx_async_engine
from xiaoxing_ai.logger import setup_logging
from xiaoxing_ai.observability import init_otlp
from xiaoxing_ai.router import api, public_api
from xiaoxing_ai.settings import settings
from xiaoxing_ai.shared.api import (
    APIException,
    api_exception_handler,
    http_exception_handler,
    validation_exception_handler,
)

security = HTTPBasic()


# ref: https://github.com/tiangolo/fastapi/issues/364#issuecomment-890853577
def get_current_username(credentials: HTTPBasicCredentials = Depends(security)):
    correct_username = secrets.compare_digest(credentials.username, settings.API_DOCS_AUTH_USERNAME)
    correct_password = secrets.compare_digest(credentials.password, settings.API_DOCS_AUTH_PASSWORD)
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='Incorrect username or password',
            headers={'WWW-Authenticate': 'Basic'},
        )
    return credentials.username


def auth_for_docs(app: FastAPI):
    @app.get('/docs', include_in_schema=False)
    async def get_swagger_documentation(username: str = Depends(get_current_username)):
        return get_swagger_ui_html(openapi_url='/openapi.json', title='docs')

    @app.get('/redoc', include_in_schema=False)
    async def get_redoc_documentation(username: str = Depends(get_current_username)):
        return get_redoc_html(openapi_url='/openapi.json', title='docs')

    @app.get('/openapi.json', include_in_schema=False)
    async def openapi(username: str = Depends(get_current_username)):
        return get_openapi(title=app.title, version=app.version, routes=app.routes)


@asynccontextmanager
async def lifespan(_: FastAPI):
    setup_logging(settings.PROD_MODE)
    langfuse_context.configure(
        public_key=settings.LANGFUSE_PUBLIC_KEY,
        secret_key=settings.LANGFUSE_SECRET_KEY,
        host=settings.LANGFUSE_HOST,
        # disable debug when production
        debug=False,
        # enabled=settings.PROD_MODE,
    )
    yield
    langfuse_context.flush()
    await async_engine.dispose()
    await gz_async_engine.dispose()
    await yypx_async_engine.dispose()


app = FastAPI(
    lifespan=lifespan,
    docs_url=None,
    redoc_url=None,
    openapi_url=None,
)

init_otlp()
FastAPIInstrumentor().instrument_app(app)
LoggingInstrumentor().instrument(set_logging_format=True)
SQLAlchemyInstrumentor().instrument(
    engines=[
        async_engine.sync_engine,
        gz_async_engine.sync_engine,
        yypx_async_engine.sync_engine,
    ]
)
RequestsInstrumentor().instrument()
HTTPXClientInstrumentor().instrument()
AsyncioInstrumentor().instrument()
RedisInstrumentor().instrument()
OpenAIInstrumentor().instrument()


if not settings.PROD_MODE:
    auth_for_docs(app)


@app.middleware('http')
async def add_process_time_header(request: Request, call_next):
    start_time = time.perf_counter()
    response = await call_next(request)
    process_time = time.perf_counter() - start_time
    response.headers['X-Process-Time'] = str(process_time)
    return response


# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)


app.include_router(public_api)
app.include_router(api)

app.add_exception_handler(APIException, api_exception_handler)  # type: ignore
app.add_exception_handler(StarletteHTTPException, http_exception_handler)  # type: ignore
app.add_exception_handler(RequestValidationError, validation_exception_handler)  # type: ignore
