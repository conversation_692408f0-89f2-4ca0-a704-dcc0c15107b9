from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

from xiaoxing_ai.settings import settings


def init_otlp(insecure: bool = True):
    # Service name is required for most backends
    resource = Resource.create(attributes={SERVICE_NAME: 'xiaoxing-ai'})

    # Configure Tracing
    trace_provider = TracerProvider(resource=resource)
    processor = BatchSpanProcessor(OTLPSpanExporter(endpoint=settings.OTLP_URL, insecure=insecure))
    trace_provider.add_span_processor(processor)
    trace.set_tracer_provider(trace_provider)

    # Configure Metrics
    # reader = PeriodicExportingMetricReader(OTLPMetricExporter(endpoint=settings.OTLP_URL, insecure=insecure))
    # meter_provider = MeterProvider(resource=resource, metric_readers=[reader])
    # metrics.set_meter_provider(meter_provider)

    # Configure Logging
    # logger_provider = LoggerProvider(resource=resource)
    # set_logger_provider(logger_provider)
    #
    # exporter = OTLPLogExporter(endpoint=settings.OTLP_URL, insecure=insecure)
    # logger_provider.add_log_record_processor(BatchLogRecordProcessor(exporter))
    # handler = LoggingHandler(level=logging.NOTSET, logger_provider=logger_provider)
    # # Attach OTLP handler to root logger
    # logging.getLogger().addHandler(handler)

    # loki_handler = LokiLoggerHandler(
    #     url=settings.LOKI_URL,  # type: ignore
    #     labels={'application': 'xiaoxing-ai',},
    #     label_keys={},
    #     timeout=10,
    # )
    #
    # logging.getLogger().addHandler(loki_handler)
    #
