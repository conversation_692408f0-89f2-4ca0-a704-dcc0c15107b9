from fastapi import APIRouter

from xiaoxing_ai.apps import api_v1, api_v2
from xiaoxing_ai.apps.common.views import router as common_router

__all__ = ['api', 'public_api']


api = APIRouter(prefix='/api')
api.include_router(api_v1)
api.include_router(api_v2)
api.include_router(common_router)

public_api = APIRouter()


@public_api.get('/')
async def index():
    return {'message': 'xiaoxing-ai server'}


@public_api.get('/health')
async def health():
    return {'status': 'ok'}
