from pydantic import Field, SecretStr, computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict
from sqlalchemy import URL

__all__ = ['settings']


class AISettings(BaseSettings):
    # Pydantic settings configuration
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', extra='ignore')

    # 通用设置
    PROD_MODE: bool = Field(default=True, description='是否为生产环境，默认开启')
    WEWORK_WEBHOOK_KEY: str = Field(..., description='企业微信群告警Webhook key')

    # API Docs
    API_DOCS_AUTH_USERNAME: str = Field(..., description='API文档用户名')
    API_DOCS_AUTH_PASSWORD: str = Field(..., description='API文档密码')

    # OpenAI服务
    OPENAI_BASE_URL: str | None = Field(default=None, description='OpenAI API Base URL')
    OPENAI_API_KEY: str = Field(..., description='OpenAI API Key')

    DEEPSEEK_BASE_URL: str = Field(..., description='deepseek API Base URL')
    DEEPSEEK_API_KEY: str = Field(..., description='deepseek API Key')

    ZHIPU_BASE_URL: str = Field(..., description='智谱 API Base URL')
    ZHIPU_API_KEY: str = Field(..., description='智谱 API Key')

    DOUBAO_BASE_URL: str = Field(..., description='豆包 API Base URL')
    DOUBAO_API_KEY: str = Field(..., description='豆包 API Key')

    QWEN_BASE_URL: str = Field(..., description='通一千问 API Base URL')
    QWEN_API_KEY: str = Field(..., description='通一千问 API Key')

    # Azure AI 配置
    AZURE_AI_ENDPOINT: str = Field(..., description='Azure AI 端点')
    AZURE_AI_API_KEY: str = Field(..., description='Azure AI API Key')
    AZURE_AI_API_VERSION: str = Field(..., description='Azure AI API 版本')

    # Langfuse大模型调用日志追踪
    LANGFUSE_SECRET_KEY: str = Field(..., description='Langfuse Secret Key')
    LANGFUSE_PUBLIC_KEY: str = Field(..., description='Langfuse Public Key')
    LANGFUSE_HOST: str = Field(..., description='Langfuse自部署链接')

    # Elasticsearch
    ES_BASE_URL: str = Field(..., description='Elasticsearch地址')
    ES_INDEX_PREFIX: str = Field(default='xiaoxing-ai', description='Elasticsearch索引名前缀')

    # Database
    DATABASE_HOST: str = Field(..., description='数据库地址')
    DATABASE_PORT: int = Field(..., description='数据库端口')
    DATABASE_USERNAME: str = Field(..., description='数据库用户名')
    DATABASE_PASSWORD: SecretStr = Field(..., description='数据库密码')
    DATABASE_DBNAME: str = Field(..., description='数据库名')

    # 广州侧 Database
    GZ_DATABASE_HOST: str = Field(..., description='数据库地址')
    GZ_DATABASE_PORT: int = Field(..., description='数据库端口')
    GZ_DATABASE_USERNAME: str = Field(..., description='数据库用户名')
    GZ_DATABASE_PASSWORD: SecretStr = Field(..., description='数据库密码')
    GZ_DATABASE_DBNAME: str = Field(..., description='数据库名')

    # AI英语/作文的数据库
    YYPX_DATABASE_HOST: str = Field(..., description='数据库地址')
    YYPX_DATABASE_PORT: int = Field(..., description='数据库端口')
    YYPX_DATABASE_USERNAME: str = Field(..., description='数据库用户名')
    YYPX_DATABASE_PASSWORD: SecretStr = Field(..., description='数据库密码')
    YYPX_DATABASE_DBNAME: str = Field(..., description='数据库名')

    # 百度ocr
    BAIDU_OCR_API_KEY: str = Field(..., description='百度OCR服务API KEY')
    BAIDU_OCR_SECRET_KEY: str = Field(..., description='百度OCR服务SECRET KEY')

    # OSS
    OSS_CDN_URL: str = Field(..., description='存储对外访问域名地址')
    OSS_ENTRYPOINT: str = Field(..., description='存储API地址')
    OSS_ACCESS_KEY: str = Field(..., description='OSS Access Key')
    OSS_SECRET_KEY: SecretStr = Field(..., description='OSS Secret Key')
    OSS_BUCKET_NAME: str = Field(..., description='OSS 存储桶名')
    OSS_BUCKET_REGION: str = Field(..., description='OSS 存储桶区域')

    # 讯飞
    XF_APP_ID: str = Field(..., description='讯飞服务的app_id')
    XF_API_KEY: str = Field(..., description='讯飞服务的api_key')
    XF_API_SECRET: str = Field(..., description='讯飞服务的api_secret')

    # 阿里云OCR
    ALIBABA_CLOUD_ACCESS_KEY_ID: str = Field(..., description='阿里云OCR AccessKey ID')
    ALIBABA_CLOUD_ACCESS_KEY_SECRET: str = Field(..., description='阿里云OCR AccessKey Secret')
    ALIBABA_CLOUD_OCR_ENDPOINT: str = Field(..., description='阿里云OCR Endpoint')

    REDIS_HOST: str = Field(default='127.0.0.1', description='Redis地址')
    REDIS_PORT: int = Field(default=6379, description='Redis端口')
    REDIS_DB: int = Field(default=0, description='Redis数据库')
    REDIS_USER: str = Field(default='', description='Redis用户名')
    REDIS_PASSWORD: str = Field(default='', description='Redis密码')

    # math jwt 相关
    JWT_SECRET_KEY: str = Field(
        default='d04b1725ce0ad8c753ddfed24f7146cf65bcfd99c09bad37b1c1ae06e06e4760',
        description='jwt密钥',
    )
    JWT_ALGORITHM: str = Field(default='HS256', description='算法')
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=60 * 24 * 7, description='访问令牌过期时间(分钟)')
    JWT_REFRESH_TOKEN_EXPIRE_MINUTES: int = Field(default=60 * 24 * 31, description='刷新令牌过期时间(分钟)')

    # 好未来
    HWL_APP_KEY: str = Field(..., description='好未来 APIkey')
    HWL_APP_SECRET: str = Field(..., description='好未来 API Secret')

    ARTICLE_CACHE_EXPIRE_SECONDS: int = Field(default=60 * 60 * 24 * 3, description='作文缓存时间')

    # Milvus向量库配置
    MILVUS_HOST: str = Field(..., description='Milvus服务地址')
    MILVUS_PORT: int = Field(..., description='Milvus服务端口')
    MILVUS_USER: str = Field(..., description='Milvus用户名')
    MILVUS_PASSWORD: SecretStr = Field(..., description='Milvus密码')
    MILVUS_COLLECTION: str = Field(..., description='Milvus集合名称')

    # otlp
    OTLP_URL: str = Field(..., description='opentelemetry 控制器的url(默认为grpc端口)')

    @computed_field(return_type=URL)
    @property
    def DATABASE_URL(self):
        url = URL.create(
            'mysql+asyncmy',
            username=self.DATABASE_USERNAME,
            password=self.DATABASE_PASSWORD.get_secret_value(),
            host=self.DATABASE_HOST,
            port=self.DATABASE_PORT,
            database=self.DATABASE_DBNAME,
        )

        return url

    @computed_field(return_type=URL)
    @property
    def GZ_DATABASE_URL(self):
        url = URL.create(
            'mysql+asyncmy',
            username=self.GZ_DATABASE_USERNAME,
            password=self.GZ_DATABASE_PASSWORD.get_secret_value(),
            host=self.GZ_DATABASE_HOST,
            port=self.GZ_DATABASE_PORT,
            database=self.GZ_DATABASE_DBNAME,
        )

        return url

    @computed_field(return_type=URL)
    @property
    def YYPX_DATABASE_URL(self):
        url = URL.create(
            'mysql+asyncmy',
            username=self.YYPX_DATABASE_USERNAME,
            password=self.YYPX_DATABASE_PASSWORD.get_secret_value(),
            host=self.YYPX_DATABASE_HOST,
            port=self.YYPX_DATABASE_PORT,
            database=self.YYPX_DATABASE_DBNAME,
        )

        return url


settings = AISettings()  # type: ignore
