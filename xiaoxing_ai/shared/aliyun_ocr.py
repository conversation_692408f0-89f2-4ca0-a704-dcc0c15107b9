from typing import Dict, List

from alibabacloud_ocr_api20210707 import models as ocr_api_20210707_models
from alibabacloud_ocr_api20210707.client import Client as ocr_api20210707Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models

from xiaoxing_ai.settings import settings


class OCRError(Exception):
    """OCR识别错误"""

    pass


class AliyunOCRService:
    def __init__(self):
        self.config = open_api_models.Config(
            access_key_id=settings.ALIBABA_CLOUD_ACCESS_KEY_ID,
            access_key_secret=settings.ALIBABA_CLOUD_ACCESS_KEY_SECRET,
            endpoint=settings.ALIBABA_CLOUD_OCR_ENDPOINT,
        )
        self.client = ocr_api20210707Client(self.config)

    async def recognize_single_question(self, image_data: bytes) -> Dict:
        """识别单题，仅支持body"""
        if not image_data:
            raise ValueError('必须提供图片二进制数据')
        request = ocr_api_20210707_models.RecognizeEduQuestionOcrRequest(body=image_data, need_rotate=True)  # type: ignore
        runtime = util_models.RuntimeOptions()
        try:
            response = await self.client.recognize_edu_question_ocr_with_options_async(request, runtime)
            return response.body.to_map()
        except Exception as e:
            raise OCRError(f'识别单题失败: {str(e)}')

    async def recognize_multiple_questions(self, image_data: bytes) -> Dict:
        """识别多题，仅支持body，直接返回原始结果"""
        if not image_data:
            raise ValueError('必须提供图片二进制数据')
        request = ocr_api_20210707_models.RecognizeEduPaperStructedRequest(
            body=image_data,  # type: ignore
            need_rotate=True,
            # subject=None,
            output_oricoord=True,
        )
        runtime = util_models.RuntimeOptions()
        try:
            response = await self.client.recognize_edu_paper_structed_with_options_async(request, runtime)
            result = response.body.to_map()
            return result
        except Exception as e:
            raise OCRError(f'识别多题失败: {str(e)}')

    async def recognize_paper(self, image_data: bytes) -> Dict:
        """识别试卷，仅支持body，直接返回原始结果"""
        if not image_data:
            raise ValueError('必须提供图片二进制数据')
        request = ocr_api_20210707_models.RecognizeEduPaperStructedRequest(
            body=image_data,  # type: ignore
            need_rotate=True,
            subject='Math',
            output_oricoord=True,
        )
        runtime = util_models.RuntimeOptions()
        try:
            response = await self.client.recognize_edu_paper_structed_with_options_async(request, runtime)
            result = response.body.to_map()
            return result
        except Exception as e:
            raise OCRError(f'识别试卷失败: {str(e)}')

    def _split_questions(self, result: Dict) -> List[Dict]:
        questions = []
        if 'data' in result and 'questions' in result['data']:
            for question in result['data']['questions']:
                questions.append(
                    {
                        'question_text': question.get('question_text', ''),
                        'answer': question.get('answer', ''),
                        'analysis': question.get('analysis', ''),
                        'question_type': question.get('question_type', ''),
                        'question_number': question.get('question_number', ''),
                    }
                )
        return questions
