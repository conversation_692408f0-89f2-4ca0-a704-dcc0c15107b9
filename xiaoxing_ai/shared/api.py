from enum import IntEnum
from typing import Any, Generic, TypeVar

from fastapi import HTTPException, Request
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, ConfigDict, Field
from starlette import status

_T = TypeVar('_T')


class BaseSchema(BaseModel):
    model_config = ConfigDict(extra='ignore')  # 忽略额外的字段


class ApiResponse(BaseSchema, Generic[_T]):
    code: int = Field(
        default=0,
        description='自定义响应码',
        examples=[
            # ok
            0,
            # internal query error
            10001,
            # internal flow broken error
            10002,
        ],
    )
    msg: str = Field(default='ok', description='响应的描述信息')
    data: _T | dict[str, Any] = Field(default_factory=dict, description='Response data')


class ErrorCode(IntEnum):
    # 400
    COMMON_ERROR = 40000  # 通用错误
    FIELD_ERROR = 40061  # 字段错误/缺失
    NOT_FOUND = 40081  # 未找到, NotFound
    # 401
    TOKEN_NOT_VALID = 40101  # token无效
    TOKEN_HAS_EXPIRED = 40102  # token已过期
    # 500
    SERVER_ERROR = 50000  # 服务器错误


# 自定义异常类
class APIException(Exception):
    def __init__(
        self,
        detail: str = '非法请求',
        code: int | ErrorCode = ErrorCode.COMMON_ERROR,
        status_code: int = 400,
        headers: dict[str, str] | None = None,
    ):
        self.detail = detail
        self.code = code
        self.status_code = status_code
        self.headers = headers


async def api_exception_handler(request: Request, exc: APIException) -> JSONResponse:
    """默认返回200"""
    response = JSONResponse(
        status_code=200,
        content=ApiResponse(
            code=exc.code,
            msg=str(exc.detail),
        ).model_dump(),
        headers=exc.headers,
    )

    return response


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    response = JSONResponse(
        status_code=exc.status_code,
        content=ApiResponse(
            code=exc.status_code,
            msg=str(exc.detail),
        ).model_dump(),
    )

    if exc.status_code == status.HTTP_401_UNAUTHORIZED:
        response.headers.update(exc.headers)  # type: ignore

    return response


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=400,
        content=ApiResponse(
            code=400,
            msg=str(exc.errors()[0]['msg']),
            data=jsonable_encoder({'detail': exc.errors(), 'body': exc.body}),
        ).model_dump(),
    )
