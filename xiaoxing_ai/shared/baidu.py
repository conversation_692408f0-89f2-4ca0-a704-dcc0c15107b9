import base64
from copy import deepcopy

import httpx
import structlog
from pydantic import BaseModel, Field

from xiaoxing_ai.settings import settings

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class Point(BaseModel):
    x: int = Field(..., description='x坐标')
    y: int = Field(..., description='y坐标')


class DocumentEnhance(BaseModel):
    log_id: int = Field(..., description='唯一的log id，用于问题定位')
    image_processed: str = Field(
        ..., description='返回处理后的图片，base64编码，如请求参数 scan_type = 1&enhance_type =0，则返回原图'
    )
    points: list[Point] | None = Field(
        default=None,
        description='检测到的图片内主体在原图中的四角点坐标，scan_type = 2 时不返回此参数',
    )
    pdf_file_size: str | None = Field(None, description='传入PDF文件的总页数，当 pdf_file 参数有效时返回该字段')


class BaiduOCR:
    """
    百度ocr服务(https://cloud.baidu.com/doc/OCR/index.html)
    """

    # 手写url
    handwriting_url = 'https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting'

    # 文档矫正增强url
    doc_enhance_url = 'https://aip.baidubce.com/rest/2.0/ocr/v1/doc_crop_enhance'

    def __init__(self):
        self.api_key = settings.BAIDU_OCR_API_KEY
        self.api_secret_key = settings.BAIDU_OCR_SECRET_KEY
        self.access_token = self._get_access_token()

    def _get_access_token(self) -> str:
        """
        使用 AK，SK 生成鉴权签名（Access Token）
        :return: access_token
        """
        url = 'https://aip.baidubce.com/oauth/2.0/token'
        params = {
            'grant_type': 'client_credentials',
            'client_id': self.api_key,
            'client_secret': self.api_secret_key,
        }

        resp = httpx.post(url, params=params)

        token = resp.json().get('access_token')
        if not token:
            raise ValueError('get access token failed')

        return token

    @staticmethod
    def _to_base64(data: bytes) -> str:
        """
        将数据转换为base64

        :param data: 文件的二进制数据
        :return: base64编码的字符串
        """
        return base64.b64encode(data).decode('utf-8')

    async def read(self, image_data: bytes) -> str:
        """识别手写图片, 返回识别到的所有文本字符串"""

        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        params = {
            'access_token': self.access_token,
        }

        data = {
            'image': self._to_base64(image_data),
            'detect_direction': 'true',
            'detect_alteration': 'false',
        }

        async with httpx.AsyncClient() as client:
            res = await client.post(self.handwriting_url, params=params, headers=headers, data=data)
            res.raise_for_status()
            result = res.json()

        await logger.ainfo('ocr result', result=result)

        words = [item['words'] for item in result.get('words_result', [])]

        return ' '.join(words)

    async def enhance_document(
        self,
        image_data: bytes,
        scan_type: int = 3,
        points: list[Point] | None = None,
        enhance_type: int = 0,
    ) -> DocumentEnhance:
        """对图片进行文档矫正增强

        Args:
            image_data: 图片二进制数据
            scan_type:
                1-只做检测返回四角点坐标;
                2-只做矫正,需传入四角点坐标;
                3-默认值，检测并矫正，返回主体在原图中的四角点坐标以及矫正后的图像
            points: 当scan_type=2时需要传入的四角点坐标
            enhance_type: 是否开启图像增强功能; 0-默认; 1-去阴影; 2-增强并锐化; 3-黑白滤镜

        Returns:
            DocumentEnhance: 处理结果
        """
        headers = {'Content-Type': 'application/json'}
        params = {
            'access_token': self.access_token,
        }

        data = {
            'image': self._to_base64(image_data),
            'scan_type': scan_type,
            'enhance_type': enhance_type,
        }

        if scan_type == 2 and points:
            # 构建四角点坐标字符串
            data['points'] = [{'x': p.x, 'y': p.y} for p in points]

        async with httpx.AsyncClient() as client:
            res = await client.post(self.doc_enhance_url, params=params, headers=headers, json=data, timeout=60)
            res.raise_for_status()
            result = res.json()

        # 去除图片的base64
        result_copy = deepcopy(result)
        result_copy.pop('image_processed')
        await logger.ainfo('document enhance result', result=result_copy)

        return DocumentEnhance(
            log_id=result['log_id'],
            image_processed=result['image_processed'],
            points=[Point(x=p['x'], y=p['y']) for p in result.get('points', [])] if result.get('points') else None,
            pdf_file_size=result.get('pdf_file_size'),
        )
