import base64
import datetime
import hashlib
import json
import mimetypes
import re
import string

import Lev<PERSON>htein
import structlog
from pydantic import BaseModel

logger = structlog.stdlib.get_logger('xiaoxing_ai')
JSON_PATTERN = re.compile(r'```json(?P<data>.*?)```', re.MULTILINE | re.DOTALL)


def md5(data: dict):
    s = json.dumps(data, sort_keys=True)
    return hashlib.md5(s.encode('utf-8')).hexdigest()


def extract_json(json_text: str) -> dict:
    s = json_text.strip()
    # s = remove_blank_characters(s)

    # Parse JSON directly at first
    try:
        return json.loads(s)
    except json.JSONDecodeError:
        pass

    if not (match := JSON_PATTERN.search(s)):
        return {}

    try:
        data = json.loads(match.groupdict().get('data', '{}'))
    except json.JSONDecodeError:
        logger.warning('Failed to parse JSON')
        return {}

    return data


def check_scores_in_range(values: BaseModel, score_limit: dict):
    """
    在模型数据被初始化时检查每个评分项的分数是否在合法范围内，
    如果超出范围则进行修正。
    """
    for field_name, (min_score, max_score) in score_limit.items():
        item = getattr(values, field_name, None)
        if item is None:
            continue

        score = item.score

        # 校正分数
        if score < min_score:
            item.score = min_score
        elif score > max_score:
            item.score = max_score

    return values


def remove_blank_characters(content: str):
    """去除空白字符(空格、制表符、换行符等）"""
    translator = str.maketrans('', '', string.whitespace)
    content = content.translate(translator)
    return content


def count_effective_chars(content: str):
    """计算文本中的有效字符数（不包含空白字符）"""
    # 使用正则表达式替换所有空白字符后计算长度
    return len(re.sub(r'\s', '', content))


def calculate_similarity(text1: str, text2: str) -> float:
    """计算两段文本的相似度（使用编辑距离）"""
    if not text1 or not text2:
        return 0.0

    # 使用归一化方式计算相似度
    similarity = Levenshtein.ratio(text1, text2)

    return similarity


def seconds_remaining() -> int:
    """
    :return: 截止到当前当日剩余秒数
    """
    today = datetime.datetime.today()
    end_date = datetime.datetime.combine(today, datetime.datetime.max.time())
    return (end_date - today).seconds


def base64_to_image(b64_str: str, is_mimetypes=False) -> tuple[bytes, str | None]:
    """
    :param b64_str: base64 图片数据
    :param is_mimetypes: 图片数据是否带有文件头
    返回 bytes 数据和文件类型
    """
    if is_mimetypes:
        header, encoded = b64_str.split(',', 1)

        # 提取mime类型, 格式: data:image/jpeg;base64,
        mime_type = header.split(';')[0].split(':')[1]

        suffix = mimetypes.guess_extension(mime_type)
    else:
        suffix = None

    return base64.b64decode(b64_str), suffix
