import base64
import hashlib
import hmac
import json
import uuid
from datetime import datetime
from typing import Any
from urllib.parse import quote

import requests
import structlog.stdlib

from xiaoxing_ai.settings import settings

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class TalException(Exception):
    """好未来错误"""

    pass


class TalNotQuestionException(TalException):
    """未搜索到题目"""

    pass


class TalParamsException(TalException):
    """参数错误"""

    pass


class TalService:
    """
    好未来搜题
    """

    base_url = 'https://openai.100tal.com'

    def __init__(self):
        self.ak = settings.HWL_APP_KEY
        self.sk = settings.HWL_APP_SECRET

    @staticmethod
    def url_format(params: dict) -> str:
        """
        # 对params进行format
        # 对 params key 进行从小到大排序
        :param params: dict()
        :return:
        a=b&c=d
        """
        sorted_parameters = sorted(params.items(), key=lambda d: d[0], reverse=False)
        string_to_sign = '&'.join(f'{k}={v}' for k, v in sorted_parameters)

        return string_to_sign

    def _generate_signature(self, params: dict):
        # 计算证书签名
        params_str = self.url_format(params)

        secret = f'{self.sk}&'

        base_string = hmac.new(
            secret.encode('utf-8'),
            params_str.encode('utf-8'),
            digestmod=hashlib.sha1,
        ).digest()

        signature = base64.b64encode(base_string).decode(encoding='utf-8')

        return signature

    def get_signature(self, url_params: dict, body_params: dict) -> dict:
        """返回url上的参数"""
        base_param = {
            'access_key_id': self.ak,
            'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S'),
            'signature_nonce': str(uuid.uuid4()),
            # 'timestamp': '2025-06-19T13:51:12',
            # 'signature_nonce': '05938256-a162-44f5-be2d-e7cd248e58ae',
        }

        sign_param = {'request_body': json.dumps(body_params)}
        sign_param.update(base_param)
        sign_param.update(url_params)

        signature = self._generate_signature(sign_param)

        base_param['signature'] = quote(signature, 'utf-8')

        return base_param

    async def search_question(
        self,
        question: str | None = None,
        image_base64: bytes | str | None = None,
        function: int = 0,
    ):
        """
        :param image_base64: 图片二进制流base64编码,仅支持JPG，JPEG、PNG格式，图片不超过1M;宽高必须大于30px，小于1080px
        :param question: 搜索文字	最大长度512字符
        :param function: 是否过滤掉答案和解析混合的题目 0：不过滤1：过滤

        """
        # https://openai.100tal.com/documents/article/page?fromWhichSys=console&id=230
        url = '/aiimage/search-questions'

        if question is None and image_base64 is None:
            raise ValueError('问题和图片不能同时为空')

        body: dict[str, Any] = {
            'function': function,
        }

        # 优先使用 图片搜索
        if image_base64:
            body['image'] = image_base64
        else:
            body['words'] = question

        headers = {'content-type': 'application/json'}

        params = self.get_signature(url_params={}, body_params=body)

        params_str = self.url_format(params)
        url = f'{self.base_url}{url}?{params_str}'

        response = requests.post(url, json=body, headers=headers)
        resp = response.json()

        # 使用httpx 报签名不一致.
        # async with httpx.AsyncClient() as client:
        #     response = await client.post(url, json=body, headers=headers)
        #     resp = response.json()

        if resp['code'] != 20000:
            await logger.aerror(f'好未来搜题接口错误: {resp}')
            match resp['code']:
                case 300017001:
                    raise TalParamsException('输入参数错误')
                case 300017052:
                    raise TalParamsException('输入图片尺寸错误')
                case 300017053:
                    raise TalNotQuestionException('未搜索到题目')
                case 300017099:
                    raise TalException('未知错误')
                case _:
                    raise TalException('其他错误')

        return resp['data']
