from enum import Str<PERSON><PERSON>
from typing import Any, Optional, Union

import structlog
from langfuse.callback import Callback<PERSON>and<PERSON>
from langfuse.decorators import langfuse_context, observe
from langfuse.openai import AsyncAzureOpenAI, AsyncOpenAI
from pydantic import BaseModel, Field, HttpUrl

from xiaoxing_ai.settings import settings

logger = structlog.stdlib.get_logger('xiaoxing_ai')

openai = AsyncOpenAI(api_key=settings.OPENAI_API_KEY, base_url=settings.OPENAI_BASE_URL, timeout=60)
deepseek = AsyncOpenAI(api_key=settings.DEEPSEEK_API_KEY, base_url=settings.DEEPSEEK_BASE_URL, timeout=30)
zhipu = AsyncOpenAI(api_key=settings.ZHIPU_API_KEY, base_url=settings.ZHIPU_BASE_URL, timeout=30)
doubao = AsyncOpenAI(api_key=settings.DOUBAO_API_KEY, base_url=settings.DOUBAO_BASE_URL, timeout=90)
azure_openai = AsyncAzureOpenAI(
    api_key=settings.AZURE_AI_API_KEY,
    azure_endpoint=settings.AZURE_AI_ENDPOINT,
    api_version=settings.AZURE_AI_API_VERSION,
)


def get_langchain_handler() -> CallbackHandler:
    langfuse_handler = langfuse_context.get_current_langchain_handler()
    if not langfuse_handler:
        langfuse_handler = CallbackHandler(
            host=settings.LANGFUSE_HOST,
            secret_key=settings.LANGFUSE_SECRET_KEY,
            public_key=settings.LANGFUSE_PUBLIC_KEY,
        )
    return langfuse_handler


class LLMParams(BaseModel):
    stream: bool = Field(default=True, description='是否流式')
    temperature: float = Field(default=0.7, description='温度')
    tools: Optional[list] = Field(default_factory=list, description='工具列表')


class LLMConfig(BaseModel):
    """LLM实例配置"""

    key: str = Field(..., description='实例key')
    provider: str = Field(..., description='服务商')
    model: str = Field(..., description='指定模型')
    api_key: str = Field(..., description='API密钥')
    base_url: HttpUrl = Field(..., description='API基础URL')
    timeout: int = Field(default=30, gt=0, description='请求超时时间(秒)')
    api_version: str = Field(default='2025-01-01-preview', description='API 版本')
    params: LLMParams = Field(default_factory=LLMParams, description='模型参数')


class ModelName(StrEnum):
    DEEPSEEK = 'deepseek-chat'
    ZHIPU = 'GLM-4-Long'
    ZHIPU_PLUS = 'glm-4-plus'
    DOUBAO = 'ep-20241016144652-wvzcj'
    DOUBAO_128k = 'doubao-lite-128k-240828'
    GPT_4O_MINI = 'gpt-4o-mini'
    GPT_4O = 'gpt-4o'
    GPT_4_1 = 'gpt-4.1'
    GEMINI = 'gemini-2.5-pro-exp-03-25'
    EMBEDDING_3 = 'embedding-3'


@observe(capture_output=False)
async def llm_chat(_llm: AsyncOpenAI, params: dict[str, Any]) -> str:
    """获取模型回答"""
    langfuse_context.update_current_trace(metadata={'url': str(_llm.base_url), 'model': params['model']})
    max_retry = 3
    while max_retry > 0:
        try:
            response = await _llm.chat.completions.create(**params)
        except Exception as e:
            raise e

        res = response.choices[0].message.content or ''
        if res:
            return res

        max_retry -= 1
        await logger.awarning('retrying')

    raise ValueError('LLM did not return any results')


@observe(as_type='generation', capture_output=False)
async def llm_chat_stream(_llm: AsyncOpenAI, params: dict[str, Any]):
    """
    Streaming version of llm_chat that yields response chunks as they become available.

    Args:
        _llm: AsyncOpenAI client instance
        params: Parameters for the chat completion API call

    Yields:
        str: Response chunks from the LLM

    Raises:
        ValueError: If LLM does not return any results after max retries
    """
    langfuse_context.update_current_trace(metadata={'url': str(_llm.base_url), 'model': params['model']})

    max_retry = 3
    while max_retry > 0:
        try:
            # Async generator
            return await _llm.chat.completions.create(**params)
        except Exception as e:
            max_retry -= 1
            if max_retry <= 0:
                raise e
            await logger.awarning('retrying streaming chat', error=str(e))

    raise ValueError('LLM did not return any results')


def get_params(llm_config: LLMConfig, messages: list) -> dict[str, Any]:
    """
    构建对话参数

    Args:
        llm_config: 实例配置
        messages: 会话上下文
    Return:
        params: 对话参数
    """

    return dict(model=llm_config.model, messages=messages, **llm_config.params.model_dump())


def get_llm(config: LLMConfig) -> Union[AsyncOpenAI, AsyncAzureOpenAI]:
    """
    创建LLM实例，支持OpenAI和Azure OpenAI两种配置

    Args:
        config: 实例参数配置

    Return:
        AsyncOpenAI或AsyncAzureOpenAI: LLM实例
    """
    if config.key.startswith('azure'):
        return AsyncAzureOpenAI(
            api_key=config.api_key,
            azure_endpoint=str(config.base_url),
            api_version=config.api_version,
        )

    return AsyncOpenAI(api_key=config.api_key, base_url=str(config.base_url), timeout=config.timeout)
