import json
from typing import Any, List, Optional, Tuple

import structlog
from pymilvus import AsyncMilvusClient, CollectionSchema, DataType, FieldSchema

from xiaoxing_ai.settings import settings
from xiaoxing_ai.shared.llms import ModelName, zhipu

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class MilvusVectorSearch:
    """Milvus向量数据库搜索助手类"""

    def __init__(self):
        """初始化基本配置，但不创建连接"""
        self.collection_name = settings.MILVUS_COLLECTION
        self.dim = 1024  # embedding-3 模型的维度为1024
        self.host = settings.MILVUS_HOST
        self.port = settings.MILVUS_PORT
        self.user = settings.MILVUS_USER
        self.password = settings.MILVUS_PASSWORD.get_secret_value()

    async def __aenter__(self):
        """异步上下文管理器入口，创建连接"""
        self.client = AsyncMilvusClient(
            uri=f'http://{self.host}:{self.port}',
            user=self.user,
            password=self.password,
        )
        return self

    async def __aexit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[Any]):
        """异步上下文管理器退出，自动关闭连接"""
        try:
            if self.client is not None:
                await self.client.close()
        except Exception as e:
            await logger.aexception('关闭Milvus连接失败', exc_info=e)

    async def close(self):
        """异步上下文管理器退出，自动关闭连接"""
        try:
            if self.client is not None:
                await self.client.close()
        except Exception as e:
            await logger.aexception('关闭Milvus连接失败', exc_info=e)

    async def _create_collection(self):
        """创建文章向量集合"""
        try:
            # 创建字段
            id_field = FieldSchema(name='id', dtype=DataType.VARCHAR, is_primary=True, max_length=36)
            embedding_field = FieldSchema(name='embedding', dtype=DataType.FLOAT_VECTOR, dim=self.dim)
            content_field = FieldSchema(name='content', dtype=DataType.VARCHAR, max_length=65535)
            result_field = FieldSchema(name='result', dtype=DataType.VARCHAR, max_length=65535)

            # 创建集合模式
            schema = CollectionSchema(
                fields=[id_field, embedding_field, content_field, result_field], description='文章向量集合'
            )

            # 创建集合
            await self.client.create_collection(collection_name=self.collection_name, schema=schema)

            # 创建索引
            index_params = {'metric_type': 'COSINE', 'index_type': 'HNSW', 'params': {'M': 8, 'efConstruction': 64}}
            await self.client.create_index(
                collection_name=self.collection_name,
                field_name='embedding',
                index_params=index_params,  # type: ignore
            )

            # 加载集合
            await self.client.load_collection(self.collection_name)

            await logger.ainfo(f'创建集合成功: {self.collection_name}, 维度: {self.dim}')
            return True
        except Exception as e:
            await logger.aexception('创建Milvus集合失败', exc_info=e)
            return False

    async def get_text_embedding(self, text: str) -> List[float]:
        """获取文本的嵌入向量"""
        try:
            # 使用模型生成嵌入向量
            response = await zhipu.embeddings.create(
                model=ModelName.EMBEDDING_3,
                input=text,
                dimensions=self.dim,
            )
            return response.data[0].embedding
        except Exception as e:
            await logger.aexception('获取文本嵌入向量失败', exc_info=e)
            return []

    async def insert_article(self, uid: str, content: str, result: str) -> bool:
        """插入文章及其嵌入向量到Milvus"""
        try:
            # 获取文章嵌入向量
            embedding = await self.get_text_embedding(content)
            if not embedding:
                return False

            # 准备插入数据
            data = [{'id': uid, 'embedding': embedding, 'content': content, 'result': result}]

            # 插入到Milvus
            await self.client.insert(collection_name=self.collection_name, data=data)
            return True
        except Exception as e:
            await logger.aexception('插入文章到Milvus失败', exc_info=e)
            return False

    async def search_similar_article_by_uids(
        self, content: str, uids: List[str], top_k: int = 1
    ) -> List[Tuple[str, float, str, dict]]:
        """根据指定的uids列表搜索相似文章

        Args:
            content: 要搜索的文章内容
            uids: 要在其中搜索的文章uid列表
            top_k: 返回的最大结果数

        Returns:
            List[Tuple[str, float, str, dict]]: 返回[(uid, 相似度, 内容, 结果)]列表
        """
        try:
            # 获取文本嵌入向量
            embedding = await self.get_text_embedding(content)
            if not embedding:
                return []

            # 使用表达式过滤，只在指定的uid中搜索
            # 构造 "id in [uid1, uid2, ...]" 的表达式
            if not uids:
                return []

            expr = f'id in [{", ".join([f'"{uid}"' for uid in uids])}]'

            # 执行搜索
            search_params = {'metric_type': 'COSINE', 'params': {'ef': 64}}
            results = await self.client.search(
                collection_name=self.collection_name,
                data=[embedding],
                anns_field='embedding',
                search_params=search_params,  # 注意这里必须是 search_params
                limit=top_k,
                filter=expr,  # 过滤表达式
                output_fields=['content', 'result'],
            )

            if not results:
                return []

            similar_articles = []
            for hits in results:
                for hit in hits:
                    uid = hit.id  # type: ignore
                    similarity = hit.score  # type: ignore # Milvus返回的是相似度分数
                    content = hit.entity.get('content', '')  # type: ignore
                    result_str = hit.entity.get('result', '{}')  # type: ignore

                    try:
                        result = json.loads(result_str)  # 转换回字典
                    except json.JSONDecodeError:
                        result = {}

                    similar_articles.append((uid, similarity, content, result))

            return similar_articles
        except Exception as e:
            await logger.aexception('根据uids搜索相似文章失败', exc_info=e)
            return []
