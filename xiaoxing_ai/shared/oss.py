import abc
import mimetypes
from dataclasses import dataclass
from hashlib import md5
from io import BytesIO
from os import PathLike
from pathlib import Path, PurePosixPath
from urllib.parse import urljoin

import structlog.stdlib
from oss2 import AuthV4, Bucket

from xiaoxing_ai.settings import settings

logger = structlog.stdlib.get_logger('xiaoxing_ai')


class FileSizeError(Exception):
    """File size error"""


class UploadError(Exception):
    """Upload file error"""


def calc_etag(data: bytes, part_size=1024 * 1024 * 5):
    """
    计算数据的 ETag 值，通过分片处理每个片段的 MD5 哈希。

    :param data: 要计算 ETag 的数据（字节串）。
    :param part_size: 每个分片的大小（字节），默认 5MB。
    :return: 计算得到的 ETag。
    """
    md5_digests = []
    for start in range(0, len(data), part_size):
        end = min(start + part_size, len(data))
        md5_digests.append(md5(data[start:end]).digest())

    if len(md5_digests) == 1:
        return md5_digests[0].hex()

    # 将字节数组拼接后计算最终的 MD5
    final_md5 = md5(b''.join(md5_digests)).hexdigest()
    return f'{final_md5}-{len(md5_digests)}'


@dataclass
class ObjectStorage(abc.ABC):
    @abc.abstractmethod
    def _upload(self, data: BytesIO, object_key: str, *args, **kwargs) -> str:
        """
        内部上传逻辑。
        :param data: 文件数据流。
        :param object_key: 对象存储的key。
        :return: 上传后的对象路径或者key。
        """
        pass

    @abc.abstractmethod
    def build_url(self, object_key: str, *args, **kwargs) -> str:
        """
        生成预签名 URL。
        :param object_key: 对象键。
        :return: 预签名 URL。
        """
        pass

    def upload_data(
        self,
        data: bytes,
        obj_key: str = '',
        prefix: str = '',
        suffix: str = '',
    ):
        """
        从字节数据上传文件。
        :param data: 字节数据。
        :param obj_key:
        :param prefix: 文件前缀(path)。
        :param suffix: 文件后缀。
        :return: 上传后的对象路径。
        """
        if obj_key:
            filename = obj_key
        else:
            filename = calc_etag(data)
        with BytesIO(data) as stream:
            path = PurePosixPath(prefix).joinpath(f'{filename}{suffix}')
            return self._upload(stream, path.as_posix())

    def upload_from_path(self, file_path: PathLike[str] | str, object_key: str | None = None, *args, **kwargs) -> str:
        """
        从文件路径上传文件。
        :param file_path: 文件路径。
        :param object_key: oss中文件的key（可选）。
        :return: 上传后的对象路径。
        """
        try:
            path = Path(file_path)
            data = path.read_bytes()
            suffix = path.suffix
        except FileNotFoundError:
            raise FileNotFoundError(f'File not found: {file_path}')
        except Exception as e:
            raise UploadError(f'Error reading file: {file_path}') from e

        etag = calc_etag(data)
        with BytesIO(data) as stream:
            key = object_key or f'{etag}{suffix}'
            return self._upload(stream, key, *args, **kwargs)


class AliyunObjectStorage(ObjectStorage):
    def __init__(self):
        _auth = AuthV4(settings.OSS_ACCESS_KEY, settings.OSS_SECRET_KEY.get_secret_value())
        self.bucket_name = settings.OSS_BUCKET_NAME

        self.bucket: Bucket = Bucket(
            auth=_auth,
            endpoint=settings.OSS_ENTRYPOINT,
            bucket_name=self.bucket_name,
            region=settings.OSS_BUCKET_REGION,
            is_cname=True,
        )

    def _upload(self, data: BytesIO, object_key: str, *args, **kwargs) -> str:
        file_size = len(data.getbuffer())
        if file_size < 1:
            raise FileSizeError('File is empty')

        content_type = mimetypes.guess_type(object_key)[0] or 'application/octet-stream'

        try:
            self.bucket.put_object(object_key, data, headers={'Content-Type': content_type})
        except Exception as e:
            logger.error(f'uploading err {str(e)}')
            raise UploadError(f'get error when uploading {object_key} to Aliyun') from e

        return object_key

    def build_url(self, object_key: str, *args, **kwargs) -> str:
        url = self.bucket.sign_url('GET', object_key, 60 * 60 * 15, slash_safe=True)
        return url

    @staticmethod
    def cdn_url(object_key: str, *args, **kwargs) -> str:
        """构建cdn_url"""
        url = urljoin(settings.OSS_CDN_URL, object_key)
        return url


oss = AliyunObjectStorage()


def convert_to_oss_urls(data: dict | list, keys: list[str]):
    """
    递归处理数据结构中的指定键值，将其转换为 OSS 链接。

    :param data: 要处理的数据结构，可以是字典或列表
    :param keys: 需要转换为 OSS 链接的键列表
    :return: 处理后的数据结构
    """
    if isinstance(data, dict):
        for key, value in data.items():
            if key in keys:
                # value 为空不进行预签名
                if not value:
                    continue
                # 对url进行预签名
                data[key] = oss.build_url(value)
            else:
                convert_to_oss_urls(value, keys)
    elif isinstance(data, list):
        for index, item in enumerate(data):
            data[index] = convert_to_oss_urls(item, keys)
    return data
