import json
from dataclasses import asdict, is_dataclass
from functools import partial
from typing import Any, Optional

_json_dumps = partial(json.dumps, ensure_ascii=False)


def _is_pydantic_model(obj: Any) -> bool:
    """Check if an object is a Pydantic model instance."""
    try:
        from pydantic import BaseModel as PydanticBaseModel

        return isinstance(obj, PydanticBaseModel)
    except ImportError:
        return False


def _serialize_data(data: Any) -> str:
    """Serialize data to JSON string, handling various input types."""
    if isinstance(data, str):
        return data
    elif isinstance(data, (dict, list, tuple, set, frozenset)):
        return _json_dumps(data)
    elif _is_pydantic_model(data):
        return data.model_dump_json()
    elif is_dataclass(data) and not isinstance(data, type):
        # Handle dataclass instances (but not dataclass types)
        return _json_dumps(asdict(data), default=str)
    elif hasattr(data, '__dict__'):
        # Handle non-pydantic objects with __dict__
        return _json_dumps(data.__dict__, default=str)
    else:
        # Try to serialize other objects
        return _json_dumps(data, default=str)


def make_sse_event(
    data: Any, *, event_type: Optional[str] = None, event_id: Optional[str] = None, retry: Optional[int] = None
) -> str:
    """Generate a Server-Sent Events (SSE) formatted message.

    Args:
        data: The event data (can be dict, str, BaseModel, or any JSON-serializable object)
        event_type: Optional event type identifier. If not provided, no event field will be included
        event_id: Optional event ID for tracking
        retry: Optional retry interval in milliseconds

    Returns:
        Formatted SSE message string

    Examples:
        >>> make_sse_event("Hello World")
        'data: "Hello World"\\n\\n'
        >>> make_sse_event({"key": "value"}, event_type="message")
        'event: message\\ndata: {"key": "value"}\\n\\n'
        >>> make_sse_event(some_pydantic_model, event_type="update")
        'event: update\\ndata: {...}\\n\\n'
        >>> make_sse_event('{"pre": "serialized"}', event_type="alert", event_id="123")
        'id: 123\\nevent: alert\\ndata: {"pre": "serialized"}\\n\\n'
    """
    lines = []

    if event_id is not None:
        lines.append(f'id: {event_id}')

    if event_type:
        lines.append(f'event: {event_type}')

    lines.append(f'data: {_serialize_data(data)}')

    if retry is not None:
        lines.append(f'retry: {retry}')

    return '\n'.join(lines) + '\n\n'  # Ensure double newline at end
