import base64
import datetime
import hashlib
import hmac
from typing import Literal
from urllib.parse import urlencode
from uuid import uuid4

import httpx
import structlog
from fastapi import HTTPException
from langfuse.decorators import langfuse_context, observe

from xiaoxing_ai.settings import settings

logger = structlog.stdlib.get_logger('xiaoxing_ai.xunfei')


class XFAuditService:
    """
    讯飞 文本合规 服务

    https://www.xfyun.cn/doc/nlp/TextModeration/API.html
    """

    post_text_url: str = 'https://audit.iflyaisol.com/audit/v2/syncText'

    post_blacklist: str = 'https://audit.iflyaisol.com/audit_res/v1/wordLib/createBlack'
    post_whitelist: str = 'https://audit.iflyaisol.com/audit_res/v1/wordLib/createWhite'

    list_lib_url: str = 'https://audit.iflyaisol.com/audit_res/v1/wordLib/list'
    delete_lib_url: str = 'https://audit.iflyaisol.com/audit_res/v1/wordLib/delete'
    add_keyword_url: str = 'https://audit.iflyaisol.com/audit_res/v1/wordLib/addWord'
    delete_keyword_url: str = 'https://audit.iflyaisol.com/audit_res/v1/wordLib/delWord'
    info_lib_url: str = 'https://audit.iflyaisol.com/audit_res/v1/wordLib/info'

    CategoryType = Literal[
        'pornDetection',  # 色情；
        'violentTerrorism',  # 暴恐；
        'political',  # 涉政；
        'lowQualityIrrigation',  # 低质量灌水；
        'contraband',  # 违禁；
        'advertisement',  # 广告；
        'uncivilizedLanguage',  # 不文明用语;
    ]

    def __init__(self):
        self.app_id = settings.XF_APP_ID
        self.api_key = settings.XF_API_KEY
        self.api_secret = settings.XF_API_SECRET

    def _get_signa(self):
        zoned_time = datetime.datetime.now(datetime.timezone.utc).strftime('%Y-%m-%dT%H:%M:%S%z')

        params = {
            'appId': self.app_id,
            'accessKeyId': self.api_key,
            'accessKeySecret': self.api_secret,
            'utc': zoned_time,
            'uuid': str(uuid4()),
        }
        params = dict(sorted(params.items(), key=lambda x: x[0], reverse=False))

        base_string = hmac.new(
            self.api_secret.encode('utf-8'),
            urlencode(params).encode('utf-8'),
            digestmod=hashlib.sha1,
        ).digest()

        signature = base64.b64encode(base_string).decode(encoding='utf-8')

        params['signature'] = signature

        return params

    async def get_resp(self, url: str, data: dict):
        params = self._get_signa()

        async with httpx.AsyncClient() as client:
            resp = await client.post(url, params=params, json=data)
            resp.raise_for_status()
            result = resp.json()

        return result

    @observe(as_type='generation', capture_output=False)
    async def text_moderate(
        self,
        content: str,
        is_match_all: Literal[0, 1] = 1,
        categories: list[CategoryType] | None = None,
        lib_ids: list[str] | None = None,
    ):
        """文本审核"""
        content = content.replace(' ', '')
        data = {
            'content': content,
            'is_match_all': is_match_all,
        }

        # 添加可选参数
        if categories:
            data['categories'] = categories
        if lib_ids:
            data['lib_ids'] = lib_ids

        resp = await self.get_resp(self.post_text_url, data)
        langfuse_context.update_current_observation(output=resp)
        return resp

    async def create_blacklist(self, name: str, category: CategoryType):
        """新增黑名单词库，此词库可以在文本合规API调用过程中，填充lib_ids集合的值，从而让对应词库生效"""
        blacklist_param = {
            'name': name,
            'suggestion': 'block',
            'category': category,
        }
        resp = await self.get_resp(self.post_blacklist, blacklist_param)
        return resp

    async def add_keyword(self, lib_id: str, word_list: list[str]):
        """根据lib_id添加黑名单词条"""
        data = {
            'lib_id': lib_id,
            'word_list': word_list,
        }
        resp = await self.get_resp(self.add_keyword_url, data)
        return resp

    async def create_whitelist(self, name: str):
        """创建白名单库"""
        whitelist_param = {'name': name}
        resp = await self.get_resp(self.post_whitelist, whitelist_param)
        return resp

    async def add_white_keyword(self, lib_id, word_list):
        """添加放行词"""
        data = {
            'lib_id': lib_id,
            'word_list': word_list,
        }
        resp = await self.get_resp(self.add_keyword_url, data)
        return resp

    async def select_lib(self, lib_id):
        """查询词库信息"""
        data = {'lib_id': lib_id}
        resp = await self.get_resp(self.info_lib_url, data)
        return resp

    async def select_lib_detail(self, lib_id):
        """根据lib_id查询词条明细"""
        data = {
            'lib_id': lib_id,
            'return_word': 'true',
        }
        resp = await self.get_resp(self.info_lib_url, data)
        return resp

    async def delete_keyword(self, lib_id: str, word_list: list[str]):
        """根据lib_id删除词条"""
        data = {
            'lib_id': lib_id,
            'word_list': word_list,
        }
        resp = await self.get_resp(self.delete_keyword_url, data)
        return resp

    async def delete_lib(self, lib_id: str):
        """根据lib_id删除词库"""
        data = {'lib_id': lib_id}
        resp = await self.get_resp(self.delete_lib_url, data)
        return resp

    async def select_lib_list(self):
        """根据appid查询账户下所有词库"""
        data = {}
        resp = await self.get_resp(self.list_lib_url, data)
        return resp

    async def audit(
        self,
        content: str,
        lib_ids: list[str] | None = None,
    ) -> tuple[bool, str]:
        """获取文本的审查结果和审查信息"""
        if lib_ids is None:
            lib_ids = ['46b8eed774a449578dd403a4294d1f5d']

        try:
            resp = await self.text_moderate(content, lib_ids=lib_ids)
        except Exception as e:
            await logger.ainfo('请求讯飞服务失败', exc_info=e)
            raise HTTPException(status_code=400, detail='内容合规审查失败')

        if resp['code'] != '000000':
            await logger.error('讯飞服务调用异常', response=resp)
            raise HTTPException(status_code=400, detail='内容合规服务异常')

        harmful = False
        audit_description = ''
        if resp['data']['result']['suggest'] != 'pass':
            harmful = True
            audit_description = resp['data']['result']['detail']['category_list'][0]['category_description']

        return harmful, audit_description
